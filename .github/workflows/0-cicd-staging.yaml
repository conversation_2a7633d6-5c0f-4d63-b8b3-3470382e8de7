name: STAGING Batuta Software Classifier CICD - Build Push Docker Deploy ArgoCD
on:
  push:
    branches:
      - 'staging'
    paths:
      - 'front/**'

jobs:
  call-ci-workflow-staging:
    if: github.ref == 'refs/heads/staging'
    uses: ./.github/workflows/1-build-image-staging.yaml
    secrets:
      AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID_STAGING }}
      AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY_STAGING }}
    with:
      ECR_REPOSITORY: batuta-staging-us-east-1-ecr-software-classifier
      AWS_REGION: us-east-1
      AWS_ACCOUNT_ID_STAGING: ${{vars.AWS_ACCOUNT_ID_STAGING }}

  tag-docker-image-staging:
    if: github.ref == 'refs/heads/staging'
    uses: ./.github/workflows/2-cd-update-tag-staging.yaml
    needs: call-ci-workflow-staging
    with:
      environment: 'staging'
      service: batuta-software-classifier
      tag: ${{needs.call-ci-workflow-staging.outputs.tag}}
      values-path: application/staging/software-classifier/batuta-software-classifier
      tag-path: 'image.tag'
    secrets:
      token: ${{ secrets.GH_TOKEN }}
