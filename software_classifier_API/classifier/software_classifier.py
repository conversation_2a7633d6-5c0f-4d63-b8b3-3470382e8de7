import os
import json
import joblib
import numpy as np
import pandas as pd
from collections import Counter
from sentence_transformers import SentenceTransformer
import warnings
warnings.filterwarnings('ignore')


class SoftwareClassifier:
    """
    Clasificador de software que usa múltiples modelos ML para predecir
    categorías y nombres comerciales de productos de software.
    """
    
    def __init__(self, models_path):
        """
        Inicializa el clasificador cargando todos los modelos necesarios.
        
        Args:
            models_path (str): Ruta donde están guardados los modelos
        """
        self.models_path = models_path
        self.models_cat = {}
        self.models_com = {}
        self.tfidf = None
        self.svd = None
        self.le_cat = None
        self.le_com = None
        self.embedding_model = None
        
        self._load_all_models()
    
    def _load_all_models(self):
        """Carga todos los modelos y transformadores necesarios."""
        print("🔄 Cargando modelos...")
        
        # Cargar modelos de clasificación para categorías (sin RandomForest)
        model_names = ['logreg_cat', 'lgb_cat', 'xgb_cat', 'ensemble_cat']
        for model_name in model_names:
            try:
                model_path = os.path.join(self.models_path, f'{model_name}.pkl')
                self.models_cat[model_name] = joblib.load(model_path)
                print(f"✓ {model_name} cargado")
            except Exception as e:
                print(f"✗ Error cargando {model_name}: {str(e)}")
        
        # Cargar modelos de clasificación para nombres comerciales (sin RandomForest)
        model_names_com = ['logreg_com', 'lgb_com', 'xgb_com', 'ensemble_com']
        for model_name in model_names_com:
            try:
                model_path = os.path.join(self.models_path, f'{model_name}.pkl')
                self.models_com[model_name] = joblib.load(model_path)
                print(f"✓ {model_name} cargado")
            except Exception as e:
                print(f"✗ Error cargando {model_name}: {str(e)}")
        
        # Cargar transformadores
        try:
            self.tfidf = joblib.load(os.path.join(self.models_path, 'tfidf_vectorizer.pkl'))
            print("✓ TF-IDF Vectorizer cargado")
        except Exception as e:
            print(f"✗ Error cargando TF-IDF: {str(e)}")
        
        try:
            self.svd = joblib.load(os.path.join(self.models_path, 'svd_transformer.pkl'))
            print("✓ SVD Transformer cargado")
        except Exception as e:
            print(f"✗ Error cargando SVD: {str(e)}")
        
        # Cargar encoders
        try:
            self.le_cat = joblib.load(os.path.join(self.models_path, 'label_encoder_category.pkl'))
            print("✓ Label Encoder Category cargado")
        except Exception as e:
            print(f"✗ Error cargando Label Encoder Category: {str(e)}")
        
        try:
            self.le_com = joblib.load(os.path.join(self.models_path, 'label_encoder_comercial.pkl'))
            print("✓ Label Encoder Comercial cargado")
        except Exception as e:
            print(f"✗ Error cargando Label Encoder Comercial: {str(e)}")
        
        # Cargar modelo de embeddings
        try:
            with open(os.path.join(self.models_path, 'embedding_info.json'), 'r') as f:
                embed_info = json.load(f)
            self.embedding_model = SentenceTransformer(embed_info['model_name'])
            print(f"✓ Modelo de embeddings {embed_info['model_name']} cargado")
        except Exception as e:
            print(f"✗ Error cargando modelo de embeddings: {str(e)}")
        
        
        print("="*50)
        print("✅ Todos los modelos cargados exitosamente!")
    
    def preprocess_text(self, name, vendor):
        """
        Preprocesa el texto combinando name y vendor.
        
        Args:
            name (str): Nombre del producto
            vendor (str): Vendedor/Marca
            
        Returns:
            np.ndarray: Features procesadas listas para predicción
        """
        # Combinar name y vendor
        combined_text = f"{name} {vendor}".strip()
        
        try:
            # Generar embeddings
            embeddings = self.embedding_model.encode([combined_text])
            
            # Aplicar TF-IDF
            tfidf_features = self.tfidf.transform([combined_text])
            
            # Aplicar SVD
            tfidf_reduced = self.svd.transform(tfidf_features.toarray())
            
            # Concatenar features
            final_features = np.hstack([embeddings, tfidf_reduced])
            
            return final_features
            
        except Exception as e:
            print(f"❌ Error en preprocesamiento: {str(e)}")
            # Retornar features vacías con la dimensión esperada
            return np.zeros((1, 768))
    
    def predict_category(self, name, vendor):
        """
        Predice la categoría usando voting de los 4 modelos (sin RandomForest).
        
        Args:
            name (str): Nombre del producto
            vendor (str): Vendedor/Marca
            
        Returns:
            str: Predicción de categoría más votada
        """
        features = self.preprocess_text(name, vendor)
        predictions = []
        
        # Obtener predicciones de todos los modelos
        for model_name, model in self.models_cat.items():
            try:
                pred = model.predict(features)[0]
                predictions.append(pred)
            except Exception as e:
                print(f"Error en predicción de {model_name}: {str(e)}")
        
        # Voting - seleccionar la predicción más frecuente
        if predictions:
            most_common_pred = Counter(predictions).most_common(1)[0][0]
            # Decodificar la predicción
            category = self.le_cat.inverse_transform([most_common_pred])[0]
            return category
        else:
            return "Error en predicción"
    
    def predict_comercial_name(self, name, vendor):
        """
        Predice el nombre comercial usando voting de los 4 modelos (sin RandomForest).
        
        Args:
            name (str): Nombre del producto
            vendor (str): Vendedor/Marca
            
        Returns:
            str: Predicción de nombre comercial más votada
        """
        features = self.preprocess_text(name, vendor)
        predictions = []
        
        # Obtener predicciones de todos los modelos
        for model_name, model in self.models_com.items():
            try:
                pred = model.predict(features)[0]
                predictions.append(pred)
            except Exception as e:
                print(f"Error en predicción de {model_name}: {str(e)}")
        
        # Voting - seleccionar la predicción más frecuente
        if predictions:
            most_common_pred = Counter(predictions).most_common(1)[0][0]
            # Decodificar la predicción
            comercial_name = self.le_com.inverse_transform([most_common_pred])[0]
            return comercial_name
        else:
            return "Error en predicción"
    
    def predict_both(self, name, vendor):
        """
        Predice tanto categoría como nombre comercial.
        
        Args:
            name (str): Nombre del producto
            vendor (str): Vendedor/Marca
            
        Returns:
            dict: Diccionario con ambas predicciones
        """
        return {
            'category': self.predict_category(name, vendor),
            'comercial_name': self.predict_comercial_name(name, vendor)
        }
    
    def is_ready(self):
        """
        Verifica si todos los modelos están cargados correctamente.
        
        Returns:
            bool: True si todos los modelos están listos
        """
        return (
            len(self.models_cat) > 0 and
            len(self.models_com) > 0 and
            self.tfidf is not None and
            self.svd is not None and
            self.le_cat is not None and
            self.le_com is not None and
            self.embedding_model is not None
        )
