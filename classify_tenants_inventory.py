#!/usr/bin/env python3
"""
Script para clasificar el inventario de software de un tenant
utilizando el endpoint batch del clasificador.
"""

import os
import json
import requests
import time
from pathlib import Path
from typing import List, Dict, Any
from collections import Counter
from tqdm import tqdm

# Configuración
API_BASE_URL = "http://localhost:5000"
BATCH_ENDPOINT = f"{API_BASE_URL}/classifier/predict/batch"
TENANT_DIR = "front/data/tenant"
BATCH_SIZE = 50  # Procesar en lotes para evitar timeouts

def load_tenant_data(tenant_file_path: str) -> List[Dict[str, Any]]:
    """
    Carga los datos de un archivo de tenant
    
    Args:
        tenant_file_path: Ruta al archivo JSON del tenant
        
    Returns:
        Lista de hosts con sus aplicaciones
    """
    try:
        with open(tenant_file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"❌ Error cargando {tenant_file_path}: {str(e)}")
        return []

def extract_applications_from_tenant(tenant_data: List[Dict], tenant_name: str) -> List[Dict[str, Any]]:
    """
    Extrae todas las aplicaciones de un tenant con referencias a su ubicación original
    
    Args:
        tenant_data: Datos del tenant (lista de hosts)
        tenant_name: Nombre del tenant
        
    Returns:
        Lista de aplicaciones con referencias para poder actualizar el JSON original
    """
    applications = []
    
    for host_idx, host in enumerate(tenant_data):
        host_id = host.get('_source', {}).get('hostId', 'unknown')
        apps = host.get('_source', {}).get('Applications', [])
        
        for app_idx, app in enumerate(apps):
            # Manejar campos que pueden ser None
            name = app.get('Name')
            vendor = app.get('Vendor')
            
            # Convertir None a string vacío y luego hacer strip
            name = (name or '').strip()
            vendor = (vendor or '').strip()
            
            if name and vendor:  # Solo incluir apps con name y vendor no vacíos
                applications.append({
                    'name': name,
                    'vendor': vendor,
                    'tenant': tenant_name,
                    'host_id': host_id,
                    'host_idx': host_idx,  # Índice del host en el array original
                    'app_idx': app_idx,    # Índice de la app en el array de aplicaciones
                    'original_app': app    # Mantener datos originales para referencia
                })
    
    return applications

def classify_applications_batch(applications: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    Clasifica una lista de aplicaciones usando el endpoint batch
    
    Args:
        applications: Lista de aplicaciones a clasificar
        
    Returns:
        Lista de aplicaciones con sus clasificaciones
    """
    # Preparar el payload para el endpoint batch
    items = [{'name': app['name'], 'vendor': app['vendor']} for app in applications]
    payload = {'items': items}
    
    try:
        response = requests.post(BATCH_ENDPOINT, json=payload, timeout=300)
        response.raise_for_status()
        
        predictions = response.json().get('predictions', [])
        
        # Combinar aplicaciones originales con predicciones
        results = []
        for app, prediction in zip(applications, predictions):
            results.append({
                'tenant': app['tenant'],
                'host_id': app['host_id'],
                'host_idx': app['host_idx'],  # Índices para actualización
                'app_idx': app['app_idx'],
                'name': app['name'],
                'vendor': app['vendor'],
                'category': prediction.get('category', 'Error'),
                'comercial_name': prediction.get('comercial_name', 'Error'),
                'original_app': app['original_app']
            })
        
        return results
        
    except requests.exceptions.RequestException as e:
        print(f"❌ Error en la petición: {str(e)}")
        return []
    except Exception as e:
        print(f"❌ Error inesperado: {str(e)}")
        return []

def update_tenant_with_classifications(tenant_data: List[Dict], results: List[Dict[str, Any]]) -> List[Dict]:
    """
    Actualiza el JSON del tenant con las clasificaciones obtenidas
    
    Args:
        tenant_data: Datos originales del tenant
        results: Lista de resultados de clasificación con índices
        
    Returns:
        Datos del tenant actualizados con Category y ComercialName
    """
    # Crear una copia profunda para no modificar los datos originales
    updated_tenant_data = json.loads(json.dumps(tenant_data))
    
    # Actualizar cada aplicación con su clasificación
    for result in results:
        if result.get('category') != 'Error' and result.get('comercial_name') != 'Error':
            host_idx = result['host_idx']
            app_idx = result['app_idx']
            
            # Verificar que los índices son válidos
            if (host_idx < len(updated_tenant_data) and 
                app_idx < len(updated_tenant_data[host_idx].get('_source', {}).get('Applications', []))):
                
                # Agregar los campos de clasificación
                app = updated_tenant_data[host_idx]['_source']['Applications'][app_idx]
                app['Category'] = result['category']
                app['ComercialName'] = result['comercial_name']
    
    return updated_tenant_data

def save_classified_tenant(tenant_data: List[Dict], tenant_file_path: Path):
    """
    Guarda el tenant clasificado sobrescribiendo el archivo original
    
    Args:
        tenant_data: Datos del tenant con clasificaciones
        tenant_file_path: Ruta del archivo original del tenant
    """
    try:
        # Crear backup del archivo original
        backup_path = tenant_file_path.with_suffix('.json.backup')
        if not backup_path.exists():
            with open(tenant_file_path, 'r', encoding='utf-8') as original:
                with open(backup_path, 'w', encoding='utf-8') as backup:
                    backup.write(original.read())
            print(f"📋 Backup creado: {backup_path.name}")
        
        # Guardar el archivo clasificado
        with open(tenant_file_path, 'w', encoding='utf-8') as f:
            json.dump(tenant_data, f, indent=2, ensure_ascii=False)
            
    except Exception as e:
        print(f"❌ Error guardando tenant clasificado: {str(e)}")

def process_in_batches(applications: List[Dict[str, Any]], tenant_name: str, tenant_data: List[Dict], tenant_file_path: Path, batch_size: int = BATCH_SIZE) -> List[Dict[str, Any]]:
    """
    Procesa las aplicaciones en lotes para evitar timeouts y actualiza el tenant progresivamente
    
    Args:
        applications: Lista de todas las aplicaciones
        tenant_name: Nombre del tenant
        tenant_data: Datos originales del tenant
        tenant_file_path: Ruta del archivo del tenant
        batch_size: Tamaño del lote
        
    Returns:
        Lista de todas las clasificaciones
    """
    all_results = []
    total_batches = (len(applications) + batch_size - 1) // batch_size
    
    # Barra de progreso con tqdm
    with tqdm(total=len(applications), 
              desc=f"🔄 Clasificando {tenant_name}", 
              unit="apps",
              bar_format="{desc}: {percentage:3.0f}%|{bar}| {n_fmt}/{total_fmt} [{elapsed}<{remaining}, {rate_fmt}]") as pbar:
        
        for i in range(0, len(applications), batch_size):
            batch_num = (i // batch_size) + 1
            batch = applications[i:i + batch_size]
            
            # Actualizar descripción de la barra
            pbar.set_description(f"🔄 Procesando lote {batch_num}/{total_batches}")
            
            results = classify_applications_batch(batch)
            if results:
                all_results.extend(results)
                
                # Actualizar el tenant con las clasificaciones de este lote
                updated_tenant_data = update_tenant_with_classifications(tenant_data, results)
                tenant_data = updated_tenant_data  # Actualizar para el siguiente lote
                
                # Guardar progresivamente
                save_classified_tenant(tenant_data, tenant_file_path)
                
                pbar.set_postfix({"✅ Guardado": f"Lote {batch_num}"})
            else:
                pbar.set_postfix({"❌ Error": f"Lote {batch_num}"})
            
            # Actualizar progreso
            pbar.update(len(batch))
            
            # Pequeña pausa entre lotes para no saturar el servidor
            if i + batch_size < len(applications):
                time.sleep(0.5)
    
    return all_results

def generate_summary(results: List[Dict[str, Any]]):
    """
    Genera un resumen de los resultados
    
    Args:
        results: Lista de resultados de clasificación
    """
    if not results:
        print("❌ No hay resultados para mostrar")
        return
    
    # Estadísticas básicas
    total_apps = len(results)
    categories = set(r['category'] for r in results if r['category'] != 'Error')
    comercial_names = set(r['comercial_name'] for r in results if r['comercial_name'] != 'Error')
    errors = sum(1 for r in results if r['category'] == 'Error' or r['comercial_name'] == 'Error')
    
    print("\n" + "="*60)
    print("📊 RESUMEN DE CLASIFICACIÓN")
    print("="*60)
    print(f"Total de aplicaciones procesadas: {total_apps}")
    print(f"Clasificaciones exitosas: {total_apps - errors}")
    print(f"Errores en clasificación: {errors}")
    print(f"Categorías únicas encontradas: {len(categories)}")
    print(f"Nombres comerciales únicos: {len(comercial_names)}")
    
    # Top 10 categorías más comunes
    category_counts = Counter(r['category'] for r in results if r['category'] != 'Error')
    print("\n🏆 Top 10 Categorías más comunes:")
    for category, count in category_counts.most_common(10):
        print(f"  {category}: {count}")
    
    # Top 10 nombres comerciales más comunes
    comercial_counts = Counter(r['comercial_name'] for r in results if r['comercial_name'] != 'Error')
    print("\n🏆 Top 10 Nombres Comerciales más comunes:")
    for comercial, count in comercial_counts.most_common(10):
        print(f"  {comercial}: {count}")

def find_tenant_file() -> Path:
    """
    Encuentra el archivo de tenant en el directorio
    
    Returns:
        Path del archivo de tenant encontrado
    """
    tenant_path = Path(TENANT_DIR)
    if not tenant_path.exists():
        raise FileNotFoundError(f"El directorio {TENANT_DIR} no existe")
    
    # Buscar archivos JSON en el directorio
    json_files = list(tenant_path.glob("*.json"))
    
    if not json_files:
        raise FileNotFoundError(f"No se encontraron archivos JSON en {TENANT_DIR}")
    
    if len(json_files) > 1:
        print(f"⚠️  Se encontraron {len(json_files)} archivos JSON:")
        for i, file in enumerate(json_files, 1):
            print(f"   {i}. {file.name}")
        print(f"📂 Procesando el primero: {json_files[0].name}")
    
    return json_files[0]

def main():
    """Función principal del script"""
    print("🚀 Iniciando clasificación de inventario del tenant...")
    
    # Verificar que la API está disponible
    try:
        health_response = requests.get(f"{API_BASE_URL}/classifier/health", timeout=10)
        health_response.raise_for_status()
        health_data = health_response.json()
        if health_data.get('classifier_ready', False):
            print("✅ API del clasificador está disponible y lista")
        else:
            print("⚠️  API disponible pero el clasificador no está listo")
    except Exception as e:
        print(f"❌ No se puede conectar a la API: {str(e)}")
        print(f"   Asegúrate de que el servidor esté corriendo en {API_BASE_URL}")
        return
    
    # Encontrar el archivo de tenant
    try:
        tenant_file = find_tenant_file()
        tenant_name = tenant_file.stem.replace('_export', '')
        print(f"📂 Procesando tenant: {tenant_name}")
        print(f"📄 Archivo: {tenant_file.name}")
    except FileNotFoundError as e:
        print(f"❌ {str(e)}")
        return
    
    # Cargar datos del tenant
    tenant_data = load_tenant_data(tenant_file)
    if not tenant_data:
        print("❌ No se pudieron cargar los datos del tenant")
        return
    
    # Extraer aplicaciones
    print(f"🔍 Extrayendo aplicaciones del tenant...")
    applications = extract_applications_from_tenant(tenant_data, tenant_name)
    
    if not applications:
        print("❌ No se encontraron aplicaciones para clasificar")
        return
    
    print(f"📊 Total de aplicaciones encontradas: {len(applications)}")
    print(f"📦 Se procesarán en lotes de {BATCH_SIZE} aplicaciones")
    total_batches = (len(applications) + BATCH_SIZE - 1) // BATCH_SIZE
    print(f"📈 Número total de lotes: {total_batches}")
    
    # Estimación de tiempo (aproximadamente 2-3 segundos por lote)
    estimated_time = total_batches * 2.5
    if estimated_time < 60:
        print(f"⏱️  Tiempo estimado: ~{estimated_time:.0f} segundos")
    else:
        minutes = estimated_time // 60
        seconds = estimated_time % 60
        print(f"⏱️  Tiempo estimado: ~{minutes:.0f}m {seconds:.0f}s")
    
    print("\n" + "="*60)
    
    # Clasificar todas las aplicaciones en lotes
    results = process_in_batches(applications, tenant_name, tenant_data, tenant_file)
    
    if results:
        print("\n" + "="*60)
        print("🎉 ¡Clasificación completada exitosamente!")
        
        # Mostrar resumen
        generate_summary(results)
        
        print(f"\n📁 Tenant clasificado guardado en: {tenant_file}")
        print(f"💡 El archivo original del tenant ahora contiene los campos 'Category' y 'ComercialName' en cada aplicación")
        print(f"📋 Se creó un backup del archivo original como: {tenant_file.with_suffix('.json.backup').name}")
    else:
        print("❌ No se pudieron obtener resultados de clasificación")

if __name__ == "__main__":
    main()
