# 🚀 Instrucciones para Levantar el Frontend con MongoDB

## ✅ Verificar que todo esté listo

### 1. Verificar archivos implementados:
Tu proyecto ahora tiene todos los archivos necesarios:

```
front/
├── lib/
│   ├── mongodb.ts              ✅ Conexión MongoDB
│   └── reviewService.ts        ✅ Servicio de reviews
├── hooks/
│   └── useReviews.ts          ✅ Hook React para reviews
├── app/api/reviews/
│   ├── correct/route.ts       ✅ API thumbs up
│   ├── edited/route.ts        ✅ API thumbs down
│   ├── stats/route.ts         ✅ API estadísticas
│   ├── search/route.ts        ✅ API búsquedas
│   └── trends/route.ts        ✅ API tendencias
└── app/page.tsx               ✅ Página principal actualizada
```

### 2. Verificar variables de entorno:
En tu `.env.local` asegúrate de tener:
```bash
MONGO_URI=tu_connection_string_mongodb
DB_CERT="-----BEGIN CERTIFICATE-----
... (tu certificado completo)
-----END PRIVATE KEY-----"
```

## 🏃‍♂️ Pasos para levantar el Frontend

### 1. Abrir terminal en la carpeta front:
```bash
cd /wsl$/Ubuntu/home/<USER>/MetaBaseQ/software-classifier/front
```

### 2. Instalar dependencias (si no está hecho):
```bash
npm install
```

### 3. Levantar el servidor de desarrollo:
```bash
npm run dev
```

### 4. Verificar que funciona:
- Ve a: `http://localhost:3000`
- Deberías ver tu aplicación Software Classifier normal
- Abre la consola del navegador (F12)
- Busca este mensaje: `✅ Conectado a MongoDB - Base de datos: software-classifier`

## 🍃 Verificar MongoDB

### 1. Abrir MongoDB Compass:
- Usa tu misma connection string
- Conéctate a tu cluster

### 2. Verificar la base de datos:
- Busca la base de datos: `software-classifier`
- Al principio estará vacía, las colecciones se crean automáticamente

### 3. Probar los botones:
1. **Selecciona un tenant** de tu dropdown
2. **Click en 👍 (Thumbs Up)** en algún software:
   - Deberías ver en consola: `🚀 Thumbs up guardado en MongoDB!`
   - En Compass aparecerá la colección `review_correct`

3. **Click en 👎 (Thumbs Down)** en algún software:
   - Se abrirá el editor
   - Cambia categoría o nombre comercial
   - Click en ✅ para confirmar
   - Deberías ver en consola: `🚀 Review editado guardado en MongoDB!`
   - En Compass aparecerá la colección `review_edited`

## 📊 Verificar que funciona correctamente

### 1. En la interfaz web:
- Verás una nueva sección azul con **"📊 MongoDB Reviews Statistics"**
- Mostrará contadores en tiempo real:
  - **Correct**: Reviews positivos
  - **Edited**: Reviews editados  
  - **Total**: Total de reviews
  - **Accuracy**: Porcentaje de precisión
  - **Cat. Changed**: Categorías cambiadas
  - **Names Changed**: Nombres comerciales cambiados

### 2. En MongoDB Compass:
**Colección `review_correct`:**
```json
{
  "_id": "ObjectId(...)",
  "software_name": "Microsoft Office",
  "category": "Productivity",
  "commercial_name": "Office 365",
  "confidence_score": 0.85,
  "reviewer": "user",
  "tenant_id": "tenant_123",
  "notes": "Tenant: Cliente XYZ",
  "created_at": "2025-06-05T..."
}
```

**Colección `review_edited`:**
```json
{
  "_id": "ObjectId(...)",
  "software_name": "Adobe Photoshop",
  "original_category": "Design",
  "original_commercial_name": "Photoshop",
  "corrected_category": "Graphics",
  "corrected_commercial_name": "Adobe Photoshop CC",
  "confidence_score": 0.75,
  "reviewer": "user",
  "tenant_id": "tenant_123",
  "notes": "Tenant: Cliente XYZ - Original category: Design...",
  "created_at": "2025-06-05T..."
}
```

## 🔧 Probar las APIs directamente

### 1. Obtener estadísticas:
```bash
curl http://localhost:3000/api/reviews/stats
```

### 2. Obtener reviews correctos:
```bash
curl http://localhost:3000/api/reviews/correct
```

### 3. Obtener reviews editados:
```bash
curl http://localhost:3000/api/reviews/edited
```

### 4. Buscar por software:
```bash
curl "http://localhost:3000/api/reviews/search?software_name=Office"
```

## 🎯 Características implementadas

### ✅ Lo que ya funciona:
- **Conexión directa** a MongoDB Atlas con certificados SSL
- **Colecciones separadas**: `review_correct` y `review_edited`
- **Thumbs up**: Guarda directamente en `review_correct`
- **Thumbs down**: Guarda datos originales y corregidos en `review_edited`
- **Estadísticas en tiempo real**: Se actualizan cada 30 segundos
- **Datos completos**: Incluye category y commercial_name originales y corregidos
- **Índices optimizados**: Para búsquedas rápidas
- **Interfaz visual**: Muestra estadísticas de MongoDB en la UI

### 🚀 Ventajas:
- **Sin archivos JSON**: Todo se guarda en MongoDB
- **Tiempo real**: Los datos se sincronizan automáticamente
- **Escalable**: MongoDB maneja millones de registros
- **Búsquedas rápidas**: Índices optimizados
- **Historial completo**: Mantiene registro de todos los cambios

## 🐛 Troubleshooting

### Error de conexión a MongoDB:
```
❌ MongoDB Error: ...
```
**Solución:**
- Verificar `MONGO_URI` en `.env.local`
- Verificar que tu IP esté whitelisteada en MongoDB Atlas
- Verificar que el certificado `DB_CERT` esté completo

### No aparecen estadísticas:
**Solución:**
- Verificar que tengas un tenant seleccionado
- Hacer click en algunos thumbs up/down para generar datos
- Recargar la página

### Error 500 en APIs:
**Solución:**
- Revisar la consola del servidor para logs específicos
- Verificar que todos los archivos estén en las rutas correctas

## 🎉 ¡Todo listo!

Tu aplicación ahora:
- ✅ Se conecta directamente a MongoDB
- ✅ Guarda thumbs up en `review_correct`
- ✅ Guarda thumbs down con datos originales y corregidos en `review_edited`
- ✅ Muestra estadísticas en tiempo real
- ✅ Mantiene historial completo de todos los reviews
- ✅ Funciona con tu interfaz existente sin cambios para el usuario

**¡A disfrutar tu nueva integración con MongoDB!** 🚀
