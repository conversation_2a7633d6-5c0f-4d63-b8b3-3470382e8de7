# Authentication Configuration
# Copy this file to .env.local and update the values

# Default credentials for simple authentication
AUTH_USERNAME=admin
AUTH_PASSWORD=password123

# Environment
NODE_ENV=development

# AWS
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_S3_BUCKET=batuta-ai

# MongoDB
MONGO_URI="mongodb+srv://db-domain.example.com/software-classifier?retryWrites=true&w=majority&ssl=true"
DB_CERT_REQUIRED=true
DB_CERT="-----B<PERSON>IN CERTIFICATE-----
... (tu certificado completo)
-----END PRIVATE KEY-----"
