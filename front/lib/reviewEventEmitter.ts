import { EventEmitter } from 'events';

class ReviewEventEmitter extends EventEmitter {
  constructor() {
    super();
    this.setMaxListeners(100); // Permitir múltiples conexiones SSE
  }

  emitReviewAdded(data: {
    type: 'correct' | 'edited';
    itemId: string;
    tenant_id: string;
    software_name: string;
    feedback: 'positive' | 'corrected';
    timestamp: number;
  }) {
    this.emit('review_added', {
      event: 'review_added',
      ...data
    });
  }

  emitReviewUpdated(data: {
    type: 'correct' | 'edited';
    itemId: string;
    tenant_id: string;
    software_name: string;
    feedback: 'positive' | 'corrected' | null;
    timestamp: number;
  }) {
    this.emit('review_updated', {
      event: 'review_updated',
      ...data
    });
  }
}

export const reviewEventEmitter = new ReviewEventEmitter();