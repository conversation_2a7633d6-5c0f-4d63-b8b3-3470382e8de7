// lib/reviewService.ts
import MongooseConnection, { ReviewCorrect, ReviewEdited, ReviewCorrectItem, ReviewEditedItem } from './mongodb';
import { ObjectId } from 'mongoose';

export class ReviewService {
  private mongodb: MongooseConnection;

  constructor() {
    this.mongodb = MongooseConnection.getInstance();
  }

  /**
   * Inicializa la conexión (llamar una vez al inicio de la app)
   */
  async initialize(): Promise<void> {
    await this.mongodb.connect();
    console.log('✅ ReviewService inicializado con Mongoose');
  }

  /**
   * Agregar review correcto (👍)
   */
  async addReviewCorrect(data: {
    software_name: string;
    category: string;
    commercial_name?: string;
    confidence_score?: number;
    reviewer?: string;
    tenant_id?: string;
    notes?: string;
  }): Promise<string> {
    const review = new ReviewCorrect({
      ...data,
      created_at: new Date(),
      confidence_score: data.confidence_score || 0,
      reviewer: data.reviewer || 'anonymous',
      tenant_id: data.tenant_id || 'default'
    });

    const savedReview = await review.save();
    console.log('✅ Review correcto agregado a MongoDB:', savedReview.software_name);
    return savedReview._id.toString();
  }

  /**
   * Agregar review editado (👎)
   */
  async addReviewEdited(data: {
    software_name: string;
    original_category: string;
    original_commercial_name?: string;
    corrected_category: string;
    corrected_commercial_name?: string;
    confidence_score?: number;
    reviewer?: string;
    tenant_id?: string;
    notes?: string;
  }): Promise<string> {
    const review = new ReviewEdited({
      ...data,
      created_at: new Date(),
      confidence_score: data.confidence_score || 0,
      reviewer: data.reviewer || 'anonymous',
      tenant_id: data.tenant_id || 'default'
    });

    const savedReview = await review.save();
    console.log('✅ Review editado agregado a MongoDB:', savedReview.software_name);
    return savedReview._id.toString();
  }

  /**
   * Obtener todos los reviews correctos
   */
  async getReviewsCorrect(tenantId?: string): Promise<ReviewCorrectItem[]> {
    const filter = tenantId ? { tenant_id: tenantId } : {};
    
    const reviews = await ReviewCorrect
      .find(filter)
      .sort({ created_at: -1 })
      .lean();
    
    return reviews.map(review => ({
      ...review,
      _id: review._id.toString()
    })) as ReviewCorrectItem[];
  }

  /**
   * Obtener todos los reviews editados
   */
  async getReviewsEdited(tenantId?: string): Promise<ReviewEditedItem[]> {
    const filter = tenantId ? { tenant_id: tenantId } : {};
    
    const reviews = await ReviewEdited
      .find(filter)
      .sort({ created_at: -1 })
      .lean();
    
    return reviews.map(review => ({
      ...review,
      _id: review._id.toString()
    })) as ReviewEditedItem[];
  }

  /**
   * Buscar por nombre de software
   */
  async searchBySoftware(softwareName: string, tenantId?: string): Promise<{
    correct: ReviewCorrectItem[];
    edited: ReviewEditedItem[];
  }> {
    const filter = {
      software_name: { $regex: softwareName, $options: 'i' },
      ...(tenantId && { tenant_id: tenantId })
    };

    const [correctReviews, editedReviews] = await Promise.all([
      ReviewCorrect.find(filter).lean(),
      ReviewEdited.find(filter).lean()
    ]);

    return {
      correct: correctReviews.map(review => ({ ...review, _id: review._id.toString() })) as ReviewCorrectItem[],
      edited: editedReviews.map(review => ({ ...review, _id: review._id.toString() })) as ReviewEditedItem[]
    };
  }

  /**
   * Buscar por categoría
   */
  async searchByCategory(category: string, tenantId?: string): Promise<{
    correct: ReviewCorrectItem[];
    edited: ReviewEditedItem[];
  }> {
    const baseFilter = tenantId ? { tenant_id: tenantId } : {};
    
    const correctFilter = {
      ...baseFilter,
      category: { $regex: category, $options: 'i' }
    };
    
    const editedFilter = {
      ...baseFilter,
      $or: [
        { original_category: { $regex: category, $options: 'i' } },
        { corrected_category: { $regex: category, $options: 'i' } }
      ]
    };

    const [correctReviews, editedReviews] = await Promise.all([
      ReviewCorrect.find(correctFilter).lean(),
      ReviewEdited.find(editedFilter).lean()
    ]);

    return {
      correct: correctReviews.map(review => ({ ...review, _id: review._id.toString() })) as ReviewCorrectItem[],
      edited: editedReviews.map(review => ({ ...review, _id: review._id.toString() })) as ReviewEditedItem[]
    };
  }

  /**
   * Buscar por nombre comercial
   */
  async searchByCommercialName(commercialName: string, tenantId?: string): Promise<{
    correct: ReviewCorrectItem[];
    edited: ReviewEditedItem[];
  }> {
    const baseFilter = tenantId ? { tenant_id: tenantId } : {};
    
    const correctFilter = {
      ...baseFilter,
      commercial_name: { $regex: commercialName, $options: 'i' }
    };
    
    const editedFilter = {
      ...baseFilter,
      $or: [
        { original_commercial_name: { $regex: commercialName, $options: 'i' } },
        { corrected_commercial_name: { $regex: commercialName, $options: 'i' } }
      ]
    };

    const [correctReviews, editedReviews] = await Promise.all([
      ReviewCorrect.find(correctFilter).lean(),
      ReviewEdited.find(editedFilter).lean()
    ]);

    return {
      correct: correctReviews.map(review => ({ ...review, _id: review._id.toString() })) as ReviewCorrectItem[],
      edited: editedReviews.map(review => ({ ...review, _id: review._id.toString() })) as ReviewEditedItem[]
    };
  }

  /**
   * Obtener estadísticas
   */
  async getStats(tenantId?: string): Promise<{
    totalCorrect: number;
    totalEdited: number;
    totalReviews: number;
    accuracyRate: number;
    categoriesChanged: number;
    commercialNamesChanged: number;
  }> {
    const filter = tenantId ? { tenant_id: tenantId } : {};

    const [totalCorrect, totalEdited] = await Promise.all([
      ReviewCorrect.countDocuments(filter),
      ReviewEdited.countDocuments(filter)
    ]);

    // Contar cuántos cambios de categoría y nombre comercial hubo
    const editedReviews = await ReviewEdited.find(filter).lean();
    
    const categoriesChanged = editedReviews.filter(review => 
      review.original_category !== review.corrected_category
    ).length;
    
    const commercialNamesChanged = editedReviews.filter(review => 
      review.original_commercial_name !== review.corrected_commercial_name
    ).length;

    const totalReviews = totalCorrect + totalEdited;
    const accuracyRate = totalReviews > 0 ? (totalCorrect / totalReviews) * 100 : 0;

    return {
      totalCorrect,
      totalEdited,
      totalReviews,
      accuracyRate: Math.round(accuracyRate * 100) / 100,
      categoriesChanged,
      commercialNamesChanged
    };
  }

  /**
   * Eliminar un review
   */
  async deleteReview(reviewId: string, isCorrect: boolean): Promise<boolean> {
    const Model = isCorrect ? ReviewCorrect : ReviewEdited;
    
    const result = await Model.deleteOne({ _id: reviewId });
    
    return result.deletedCount > 0;
  }

  /**
   * Obtener tendencias de correcciones más comunes
   */
  async getCorrectionTrends(tenantId?: string): Promise<{
    categoryCorrections: Array<{ from: string; to: string; count: number }>;
    commercialNameCorrections: Array<{ from: string; to: string; count: number }>;
  }> {
    const filter = tenantId ? { tenant_id: tenantId } : {};

    // Agregaciones para obtener las correcciones más comunes
    const [categoryCorrections, commercialNameCorrections] = await Promise.all([
      // Correcciones de categoría
      ReviewEdited.aggregate([
        { $match: filter },
        { 
          $match: { 
            $expr: { $ne: ["$original_category", "$corrected_category"] } 
          } 
        },
        {
          $group: {
            _id: {
              from: "$original_category",
              to: "$corrected_category"
            },
            count: { $sum: 1 }
          }
        },
        {
          $project: {
            from: "$_id.from",
            to: "$_id.to",
            count: 1,
            _id: 0
          }
        },
        { $sort: { count: -1 } },
        { $limit: 10 }
      ]),

      // Correcciones de nombre comercial
      ReviewEdited.aggregate([
        { $match: filter },
        { 
          $match: { 
            $expr: { $ne: ["$original_commercial_name", "$corrected_commercial_name"] } 
          } 
        },
        {
          $group: {
            _id: {
              from: "$original_commercial_name",
              to: "$corrected_commercial_name"
            },
            count: { $sum: 1 }
          }
        },
        {
          $project: {
            from: "$_id.from",
            to: "$_id.to",
            count: 1,
            _id: 0
          }
        },
        { $sort: { count: -1 } },
        { $limit: 10 }
      ])
    ]);

    return {
      categoryCorrections,
      commercialNameCorrections
    };
  }
}

// Exportar tipos para usar en otros archivos
export type { ReviewCorrectItem, ReviewEditedItem };

// Instancia singleton
export const reviewService = new ReviewService();
