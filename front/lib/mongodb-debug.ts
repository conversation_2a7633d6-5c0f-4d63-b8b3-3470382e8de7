// lib/mongodb-debug.ts
import { MongoClient, Db, Collection } from 'mongodb';

// Debug version para diagnosticar problemas
export class MongoDBDebug {
  private static instance: MongoDBDebug;
  private client: MongoClient;
  private db: Db;

  private constructor() {}

  static getInstance(): MongoDBDebug {
    if (!MongoDBDebug.instance) {
      MongoDBDebug.instance = new MongoDBDebug();
    }
    return MongoDBDebug.instance;
  }

  async testConnection(): Promise<{ success: boolean; error?: string; details?: any }> {
    try {
      const uri = process.env.MONGO_URI;
      const cert = process.env.DB_CERT;

      console.log('🔍 Debugging MongoDB connection...');
      console.log('URI exists:', !!uri);
      console.log('URI preview:', uri?.substring(0, 50) + '...');
      console.log('Certificate exists:', !!cert);
      console.log('Certificate length:', cert?.length);

      if (!uri) {
        return { success: false, error: 'MONGO_URI is not defined in environment variables' };
      }

      if (!cert) {
        return { success: false, error: 'DB_CERT is not defined in environment variables' };
      }

      // Check if URI has credentials
      if (!uri.includes('@')) {
        return { success: false, error: 'MONGO_URI is missing credentials (username:password@...)' };
      }

      // Create temporary certificate file
      const fs = require('fs');
      const path = require('path');
      const certPath = path.join(process.cwd(), 'debug-cert.pem');
      
      try {
        fs.writeFileSync(certPath, cert);
        console.log('✅ Certificate file written successfully');
      } catch (certError) {
        return { success: false, error: 'Failed to write certificate file', details: certError };
      }

      // Test MongoDB connection
      console.log('🔗 Attempting MongoDB connection...');
      
      this.client = new MongoClient(uri, {
        ssl: true,
        tlsAllowInvalidCertificates: false,
        tlsCertificateKeyFile: certPath,
        maxPoolSize: 10,
        serverSelectionTimeoutMS: 10000, // 10 seconds timeout
        connectTimeoutMS: 10000,
      });

      await this.client.connect();
      console.log('✅ Connected to MongoDB successfully');

      // Test database access
      this.db = this.client.db('software-classifier');
      const dbStats = await this.db.stats();
      console.log('✅ Database access successful');

      // Clean up certificate file
      fs.unlinkSync(certPath);

      await this.client.close();

      return { 
        success: true, 
        details: { 
          dbName: 'software-classifier',
          dbStats: {
            collections: dbStats.collections,
            dataSize: dbStats.dataSize,
            storageSize: dbStats.storageSize
          }
        }
      };

    } catch (error: any) {
      console.error('❌ MongoDB connection failed:', error);
      
      // Clean up certificate file if it exists
      try {
        const fs = require('fs');
        const path = require('path');
        const certPath = path.join(process.cwd(), 'debug-cert.pem');
        if (fs.existsSync(certPath)) {
          fs.unlinkSync(certPath);
        }
      } catch {}

      return { 
        success: false, 
        error: error.message || 'Unknown connection error',
        details: {
          code: error.code,
          codeName: error.codeName,
          name: error.name
        }
      };
    }
  }
}

export default MongoDBDebug;
