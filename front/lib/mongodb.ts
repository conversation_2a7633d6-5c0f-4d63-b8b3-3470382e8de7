import mongoose from 'mongoose';

// Interfaces para los documentos
export interface ReviewCorrectItem {
  _id?: string;
  software_name: string;
  category: string;
  commercial_name?: string;
  confidence_score?: number;
  reviewer?: string;
  tenant_id?: string;
  notes?: string;
  created_at: Date;
}

export interface ReviewEditedItem {
  _id?: string;
  software_name: string;
  // Datos originales
  original_category: string;
  original_commercial_name?: string;
  // Datos corregidos
  corrected_category: string;
  corrected_commercial_name?: string;
  confidence_score?: number;
  reviewer?: string;
  tenant_id?: string;
  notes?: string;
  created_at: Date;
}

// Esquemas de Mongoose
const ReviewCorrectSchema = new mongoose.Schema({
  software_name: { type: String, required: true, index: true },
  category: { type: String, required: true, index: true },
  commercial_name: { type: String, index: true },
  confidence_score: { type: Number, default: 0 },
  reviewer: { type: String, default: 'anonymous' },
  tenant_id: { type: String, index: true },
  notes: { type: String },
  created_at: { type: Date, default: Date.now, index: -1 }
}, {
  collection: 'review_correct'
});

const ReviewEditedSchema = new mongoose.Schema({
  software_name: { type: String, required: true, index: true },
  original_category: { type: String, required: true, index: true },
  original_commercial_name: { type: String, index: true },
  corrected_category: { type: String, required: true, index: true },
  corrected_commercial_name: { type: String, index: true },
  confidence_score: { type: Number, default: 0 },
  reviewer: { type: String, default: 'anonymous' },
  tenant_id: { type: String, index: true },
  notes: { type: String },
  created_at: { type: Date, default: Date.now, index: -1 }
}, {
  collection: 'review_edited'
});

// Modelos de Mongoose
export const ReviewCorrect = mongoose.models.ReviewCorrect || mongoose.model('ReviewCorrect', ReviewCorrectSchema);
export const ReviewEdited = mongoose.models.ReviewEdited || mongoose.model('ReviewEdited', ReviewEditedSchema);

class MongooseConnection {
  private static instance: MongooseConnection;
  private isConnected: boolean = false;

  private constructor() {}

  static getInstance(): MongooseConnection {
    if (!MongooseConnection.instance) {
      MongooseConnection.instance = new MongooseConnection();
    }
    return MongooseConnection.instance;
  }

  async connect(): Promise<void> {
    if (this.isConnected && mongoose.connection.readyState === 1) {
      console.log('✅ Ya conectado a MongoDB con Mongoose (certificado)');
      return;
    }

    try {
      const uri = process.env.MONGO_URI;
      const cert = process.env.DB_CERT;
      const certRequired = process.env.DB_CERT_REQUIRED === 'true';

      if (!uri) {
        throw new Error('MONGO_URI no está definida en las variables de entorno');
      }

      console.log('🔗 Conectando a MongoDB con autenticación por certificado...');
      console.log('🔍 URI:', uri.substring(0, 50) + '...');
      console.log('📜 Certificado disponible:', !!cert);
      console.log('🔒 Autenticación requerida:', certRequired);

      // Configurar opciones de conexión
      const dbConnectOptions: any = {};

      if (certRequired && cert) {
        console.log('📄 Configurando autenticación por certificado...');
        
        // Crear archivo de certificado como en el ejemplo
        const fs = require('fs');
        const path = require('path');
        const mongoCertPath = path.join(process.cwd(), 'db-cert.pem');
        
        fs.writeFileSync(mongoCertPath, cert);
        console.log('✅ Certificado escrito en:', mongoCertPath);

        // Configuración SSL para autenticación por certificado
        dbConnectOptions.ssl = true;
        dbConnectOptions.tlsAllowInvalidCertificates = false;
        dbConnectOptions.tlsCertificateKeyFile = mongoCertPath;
        dbConnectOptions.maxPoolSize = 10;
        
        // Opciones adicionales para autenticación por certificado
        dbConnectOptions.authMechanism = 'MONGODB-X509';
        dbConnectOptions.authSource = '$external';
        
        console.log('🔐 Configuración SSL completa');
      } else {
        console.log('⚠️  Conectando sin certificado (modo desarrollo)');
      }

      await mongoose.connect(uri, dbConnectOptions);

      this.isConnected = true;
      console.log('✅ ¡Conectado a MongoDB con autenticación por certificado!');
      console.log('📁 Base de datos: software-classifier');
      console.log('📊 Colecciones: review_correct, review_edited');
      console.log('🔒 Método de autenticación: X.509 Certificate');

    } catch (error: any) {
      console.error('❌ Error conectando a MongoDB:', error.message);
      console.error('📝 Detalles del error:', {
        name: error.name,
        code: error.code,
        codeName: error.codeName
      });
      throw error;
    }
  }

  async disconnect(): Promise<void> {
    if (this.isConnected) {
      await mongoose.disconnect();
      this.isConnected = false;
      console.log('✅ Desconectado de MongoDB');
    }
  }

  getConnectionStatus(): boolean {
    return this.isConnected && mongoose.connection.readyState === 1;
  }
}

export default MongooseConnection;