'use client';

import { useState, useRef, useEffect } from 'react';
import { ChevronDown, Search } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';

interface SearchableFilterDropdownProps {
  value: string;
  onChange: (value: string) => void;
  options: string[];
  placeholder: string;
  allLabel: string;
  disabled?: boolean;
  className?: string;
}

export function SearchableFilterDropdown({
  value,
  onChange,
  options,
  placeholder,
  allLabel,
  disabled = false,
  className = '',
}: SearchableFilterDropdownProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedIndex, setSelectedIndex] = useState(-1);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const optionsContainerRef = useRef<HTMLDivElement>(null);

  // Filter options based on search term
  const filteredOptions = options.filter(option =>
    option.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // All available options including "All" option
  const allOptions = ['all', ...filteredOptions];

  // Close dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
        setSearchTerm('');
        setSelectedIndex(-1);
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Focus input when dropdown opens
  useEffect(() => {
    if (isOpen && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isOpen]);

  // Reset selected index when search term changes
  useEffect(() => {
    setSelectedIndex(-1);
  }, [searchTerm]);

  // Auto-scroll to selected option
  useEffect(() => {
    if (selectedIndex >= 0 && optionsContainerRef.current) {
      const selectedElement = optionsContainerRef.current.children[selectedIndex] as HTMLElement;
      if (selectedElement) {
        selectedElement.scrollIntoView({
          behavior: 'smooth',
          block: 'nearest'
        });
      }
    }
  }, [selectedIndex]);

  // Handle keyboard navigation
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!isOpen) return;

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedIndex(prev => 
          prev < allOptions.length - 1 ? prev + 1 : prev
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setSelectedIndex(prev => prev > 0 ? prev - 1 : 0);
        break;
      case 'Enter':
        e.preventDefault();
        if (selectedIndex >= 0 && selectedIndex < allOptions.length) {
          handleSelect(allOptions[selectedIndex]);
        } else if (filteredOptions.length > 0) {
          // If no option is selected but there are filtered options, select the first one
          handleSelect(filteredOptions[0]);
        }
        break;
      case 'Escape':
        setIsOpen(false);
        setSearchTerm('');
        setSelectedIndex(-1);
        break;
    }
  };

  // Handle option selection
  const handleSelect = (option: string) => {
    onChange(option);
    setIsOpen(false);
    setSearchTerm('');
    setSelectedIndex(-1);
  };

  // Get display text for button
  const getDisplayText = () => {
    if (disabled) return 'Loading...';
    if (value === 'all') return allLabel;
    return value;
  };

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      <Button
        type='button'
        variant='outline'
        className='w-48 flex justify-between items-center'
        onClick={() => !disabled && setIsOpen(!isOpen)}
        disabled={disabled}
      >
        <span className='truncate'>
          {getDisplayText()}
        </span>
        <ChevronDown className='h-4 w-4 ml-2 flex-shrink-0' />
      </Button>

      {isOpen && (
        <div className='absolute z-50 w-full mt-1 bg-white border border-gray-200 rounded-md shadow-lg max-h-80 overflow-hidden'>
          {/* Search input */}
          <div className='p-2 border-b border-gray-100'>
            <div className='relative'>
              <Search className='absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400' />
              <Input
                ref={inputRef}
                type='text'
                placeholder={`Search ${placeholder.toLowerCase()}...`}
                value={searchTerm}
                onChange={e => setSearchTerm(e.target.value)}
                onKeyDown={handleKeyDown}
                className='pl-8 h-8 text-sm'
              />
            </div>
          </div>

          {/* Options list */}
          <div ref={optionsContainerRef} className='max-h-60 overflow-y-auto'>
            {/* "All" option */}
            <button
              className={`w-full px-3 py-2 text-left text-sm hover:bg-gray-100 transition-colors ${
                selectedIndex === 0 ? 'bg-gray-100' : ''
              } ${value === 'all' ? 'bg-blue-50 text-blue-700 font-medium' : ''}`}
              onClick={() => handleSelect('all')}
              onMouseEnter={() => setSelectedIndex(0)}
            >
              {allLabel}
            </button>

            {/* Filtered options */}
            {filteredOptions.length > 0 ? (
              filteredOptions.map((option, index) => {
                const actualIndex = index + 1; // +1 because "All" option is at index 0
                return (
                  <button
                    key={option}
                    className={`w-full px-3 py-2 text-left text-sm hover:bg-gray-100 transition-colors ${
                      selectedIndex === actualIndex ? 'bg-gray-100' : ''
                    } ${value === option ? 'bg-blue-50 text-blue-700 font-medium' : ''}`}
                    onClick={() => handleSelect(option)}
                    onMouseEnter={() => setSelectedIndex(actualIndex)}
                  >
                    {option}
                  </button>
                );
              })
            ) : searchTerm ? (
              <div className='px-3 py-2 text-gray-500 text-sm'>
                No options found for "{searchTerm}"
              </div>
            ) : null}
          </div>
        </div>
      )}
    </div>
  );
} 