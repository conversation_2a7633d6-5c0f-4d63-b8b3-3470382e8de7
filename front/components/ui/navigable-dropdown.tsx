'use client';

import { useState, useRef, useEffect } from 'react';
import { ChevronDown, Search, Plus, Check } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';

interface NavigableDropdownProps {
  // Core functionality
  value: string;
  onChange: (value: string) => void;
  options: string[];
  placeholder: string;
  
  // Filter-specific features
  showAllOption?: boolean;        // Enable "All" option for filters
  allLabel?: string;             // Label for "All" option
  
  // Edit-mode specific features  
  allowNew?: boolean;            // Enable "Add new" functionality
  onAddNew?: (value: string) => Promise<boolean>;  // Callback for adding
  strict?: boolean;              // Enforce selection from list only
  
  // Common features
  disabled?: boolean;
  className?: string;
}

export function NavigableDropdown({
  value,
  onChange,
  options,
  placeholder,
  showAllOption = false,
  allLabel = 'All',
  allowNew = false,
  onAddNew,
  strict = false,
  disabled = false,
  className = '',
}: NavigableDropdownProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedIndex, setSelectedIndex] = useState(-1);
  const [isEditing, setIsEditing] = useState(false);
  const [newValue, setNewValue] = useState('');
  const [isAdding, setIsAdding] = useState(false);
  
  const dropdownRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const optionsContainerRef = useRef<HTMLDivElement>(null);

  // Filter options based on search term
  const filteredOptions = options.filter(option =>
    option.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Build complete options array for navigation
  const allOptions = [
    ...(showAllOption ? ['all'] : []),
    ...filteredOptions,
    ...(allowNew && 
        searchTerm && 
        !options.some(opt => opt.toLowerCase() === searchTerm.toLowerCase())
        ? ['__ADD_NEW__'] : [])
  ];

  // Close dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
        setIsEditing(false);
        setSearchTerm('');
        setSelectedIndex(-1);
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Focus input when dropdown opens
  useEffect(() => {
    if (isOpen && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isOpen]);

  // Reset selected index when search term changes
  useEffect(() => {
    setSelectedIndex(-1);
  }, [searchTerm]);

  // Auto-scroll to selected option
  useEffect(() => {
    if (selectedIndex >= 0 && optionsContainerRef.current) {
      const selectedElement = optionsContainerRef.current.children[selectedIndex] as HTMLElement;
      if (selectedElement) {
        selectedElement.scrollIntoView({
          behavior: 'smooth',
          block: 'nearest'
        });
      }
    }
  }, [selectedIndex]);

  // Auto-scroll to ensure dropdown is visible when opened (for edit mode)
  useEffect(() => {
    if (isOpen && dropdownRef.current && !showAllOption) { // Only for edit mode dropdowns
      const triggerRect = dropdownRef.current.getBoundingClientRect();
      const dropdownHeight = 300;
      const viewportHeight = window.innerHeight;
      const spaceBelow = viewportHeight - triggerRect.bottom;
      const spaceAbove = triggerRect.top;
      
      if (spaceBelow < dropdownHeight && spaceAbove > spaceBelow) {
        const dropdownTop = triggerRect.top - dropdownHeight;
        if (dropdownTop < 20) {
          const scrollAmount = Math.abs(dropdownTop) + 20;
          window.scrollBy({ top: -scrollAmount, behavior: 'smooth' });
        }
      } else if (spaceBelow < dropdownHeight) {
        const scrollAmount = dropdownHeight - spaceBelow + 20;
        window.scrollBy({ top: scrollAmount, behavior: 'smooth' });
      }
    }
  }, [isOpen, showAllOption]);

  // Handle keyboard navigation
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!isOpen) return;

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedIndex(prev => 
          prev < allOptions.length - 1 ? prev + 1 : prev
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setSelectedIndex(prev => prev > 0 ? prev - 1 : 0);
        break;
      case 'Enter':
        e.preventDefault();
        if (selectedIndex >= 0) {
          const selectedOption = allOptions[selectedIndex];
          if (selectedOption === '__ADD_NEW__') {
            // Handle "Add new" selection
            setIsEditing(true);
            setNewValue(searchTerm);
          } else {
            handleSelect(selectedOption);
          }
        } else if (filteredOptions.length > 0) {
          // If no option is selected but there are filtered options, select the first one
          const firstOption = showAllOption ? 'all' : filteredOptions[0];
          handleSelect(firstOption);
        }
        break;
      case 'Escape':
        setIsOpen(false);
        setIsEditing(false);
        setSearchTerm('');
        setSelectedIndex(-1);
        break;
    }
  };

  // Handle option selection
  const handleSelect = (option: string) => {
    onChange(option);
    setIsOpen(false);
    setSearchTerm('');
    setSelectedIndex(-1);
  };

  // Handle adding new value
  const handleAddNew = async () => {
    if (!newValue.trim() || !onAddNew) return;

    setIsAdding(true);
    try {
      const success = await onAddNew(newValue.trim());
      if (success) {
        onChange(newValue.trim());
        setIsOpen(false);
        setIsEditing(false);
        setNewValue('');
      }
    } catch (error) {
      console.error('Error adding new value:', error);
    } finally {
      setIsAdding(false);
    }
  };

  // Get display text for button
  const getDisplayText = () => {
    if (disabled) return 'Loading...';
    if (showAllOption && value === 'all') return allLabel;
    return value || placeholder;
  };

  // Validate if the value is valid (for strict mode)
  const isValidValue = !strict || options.includes(value) || value === '';

  // Determine dropdown positioning style (for edit mode)
  const getDropdownStyle = () => {
    if (showAllOption) {
      // Filter dropdown - use standard positioning
      return {};
    }

    // Edit mode dropdown - use fixed positioning to avoid scroll issues
    const triggerRect = dropdownRef.current?.getBoundingClientRect();
    if (!triggerRect) return {};

    const dropdownHeight = 300;
    const viewportHeight = window.innerHeight;
    const spaceBelow = viewportHeight - triggerRect.bottom;
    const spaceAbove = triggerRect.top;
    
    const shouldPositionAbove = spaceBelow < dropdownHeight && spaceAbove > spaceBelow;
    
    return {
      position: 'fixed' as const,
      zIndex: 9999,
      width: triggerRect.width,
      top: shouldPositionAbove 
        ? triggerRect.top - dropdownHeight - 4
        : triggerRect.bottom + 4,
      left: triggerRect.left,
      maxHeight: shouldPositionAbove 
        ? Math.min(dropdownHeight, spaceAbove - 20)
        : Math.min(dropdownHeight, spaceBelow - 20),
    };
  };

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      <Button
        type='button'
        variant='outline'
        className={`${showAllOption ? 'w-48' : 'w-full'} flex justify-between items-center ${
          !isValidValue ? 'border-red-500 text-red-600' : ''
        }`}
        onClick={() => !disabled && setIsOpen(!isOpen)}
        disabled={disabled}
      >
        <span className='truncate text-left'>
          {getDisplayText()}
        </span>
        <ChevronDown className='h-4 w-4 ml-2 flex-shrink-0' />
      </Button>

      {isOpen && (
        <div 
          className={`absolute z-50 w-full mt-1 bg-white border border-gray-200 rounded-md shadow-lg max-h-80 overflow-hidden ${
            !showAllOption ? 'z-[9999]' : ''
          }`}
          style={getDropdownStyle()}
        >
          {/* Search input */}
          <div className='p-2 border-b border-gray-100'>
            <div className='relative'>
              <Search className='absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400' />
              <Input
                ref={inputRef}
                type='text'
                placeholder={`Search ${placeholder.toLowerCase()}...`}
                value={searchTerm}
                onChange={e => setSearchTerm(e.target.value)}
                onKeyDown={handleKeyDown}
                className='pl-8 h-8 text-sm'
              />
            </div>
          </div>

          {/* Options list */}
          <div ref={optionsContainerRef} className='max-h-60 overflow-y-auto'>
            {/* "All" option (for filters) */}
            {showAllOption && (
              <button
                className={`w-full px-3 py-2 text-left text-sm hover:bg-gray-100 transition-colors ${
                  selectedIndex === 0 ? 'bg-gray-100' : ''
                } ${value === 'all' ? 'bg-blue-50 text-blue-700 font-medium' : ''}`}
                onClick={() => handleSelect('all')}
                onMouseEnter={() => setSelectedIndex(0)}
              >
                {allLabel}
              </button>
            )}

            {/* Filtered options */}
            {filteredOptions.length > 0 ? (
              filteredOptions.map((option, index) => {
                const actualIndex = index + (showAllOption ? 1 : 0);
                return (
                  <button
                    key={option}
                    className={`w-full px-3 py-2 text-left text-sm hover:bg-gray-100 transition-colors flex items-center justify-between ${
                      selectedIndex === actualIndex ? 'bg-gray-100' : ''
                    } ${value === option ? 'bg-blue-50 text-blue-700 font-medium' : ''}`}
                    onClick={() => handleSelect(option)}
                    onMouseEnter={() => setSelectedIndex(actualIndex)}
                  >
                    <span>{option}</span>
                    {!showAllOption && value === option && (
                      <Check className='h-4 w-4 text-green-600' />
                    )}
                  </button>
                );
              })
            ) : searchTerm ? (
              <div className='px-3 py-2 text-gray-500 text-sm'>
                No options found for "{searchTerm}"
              </div>
            ) : null}

            {/* "Add new" option */}
            {allowNew &&
              searchTerm &&
              !options.some(
                opt => opt.toLowerCase() === searchTerm.toLowerCase()
              ) && (
                <div className='border-t'>
                  <button
                    className={`w-full px-3 py-2 text-left hover:bg-gray-100 flex items-center text-blue-600 ${
                      selectedIndex === allOptions.length - 1 ? 'bg-gray-100' : ''
                    }`}
                    onClick={() => {
                      setIsEditing(true);
                      setNewValue(searchTerm);
                    }}
                    onMouseEnter={() => setSelectedIndex(allOptions.length - 1)}
                  >
                    <Plus className='h-4 w-4 mr-2' />
                    Add "{searchTerm}"
                  </button>
                </div>
              )}

            {/* Generic "Add new" option */}
            {allowNew && !isEditing && !searchTerm && (
              <div className='border-t'>
                <button
                  className='w-full px-3 py-2 text-left hover:bg-gray-100 flex items-center text-blue-600'
                  onClick={() => {
                    setIsEditing(true);
                    setNewValue('');
                  }}
                >
                  <Plus className='h-4 w-4 mr-2' />
                  Add new value...
                </button>
              </div>
            )}
          </div>

          {/* Editor for new value */}
          {isEditing && (
            <div className='border-t p-2 bg-gray-50'>
              <div className='flex space-x-2'>
                <Input
                  placeholder='Enter new value...'
                  value={newValue}
                  onChange={e => setNewValue(e.target.value)}
                  className='h-8 flex-1'
                  onKeyDown={e => {
                    if (e.key === 'Enter') {
                      e.preventDefault();
                      handleAddNew();
                    } else if (e.key === 'Escape') {
                      setIsEditing(false);
                      setNewValue('');
                    }
                  }}
                  autoFocus
                />
                <Button
                  size='sm'
                  onClick={handleAddNew}
                  disabled={!newValue.trim() || isAdding}
                  className='h-8 px-3'
                >
                  {isAdding ? '...' : 'Add'}
                </Button>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Error message for invalid values */}
      {!isValidValue && (
        <div className='text-red-500 text-xs mt-1'>
          Please select a valid option from the list
        </div>
      )}
    </div>
  );
} 