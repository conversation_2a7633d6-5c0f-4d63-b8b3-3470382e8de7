'use client';

import { useState, useRef, useEffect } from 'react';
import { ChevronDown, Plus, Check } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';

interface SelectiveDropdownProps {
  value: string;
  onChange: (value: string) => void;
  options: string[];
  placeholder: string;
  allowNew?: boolean;
  onAddNew?: (value: string) => Promise<boolean>;
  strict?: boolean; // Si es true, solo permite valores de la lista
  className?: string;
}

export function SelectiveDropdown({
  value,
  onChange,
  options,
  placeholder,
  allowNew = false,
  onAddNew,
  strict = false,
  className = '',
}: SelectiveDropdownProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [isEditing, setIsEditing] = useState(false);
  const [newValue, setNewValue] = useState('');
  const [isAdding, setIsAdding] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Filtrar opciones basado en búsqueda
  const filteredOptions = options.filter(option =>
    option.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Cerrar dropdown al hacer clic fuera
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
        setIsEditing(false);
        setSearchTerm('');
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Auto-scroll to ensure dropdown is visible when opened
  useEffect(() => {
    if (isOpen && dropdownRef.current) {
      const triggerRect = dropdownRef.current.getBoundingClientRect();
      const dropdownHeight = 300;
      const viewportHeight = window.innerHeight;
      const spaceBelow = viewportHeight - triggerRect.bottom;
      const spaceAbove = triggerRect.top;
      
      // If positioning above and dropdown would be cut off at top
      if (spaceBelow < dropdownHeight && spaceAbove > spaceBelow) {
        const shouldPositionAbove = true;
        const dropdownTop = triggerRect.top - dropdownHeight;
        
        if (dropdownTop < 20) {
          // Scroll up to make room for dropdown above
          const scrollAmount = Math.abs(dropdownTop) + 20;
          window.scrollBy({ top: -scrollAmount, behavior: 'smooth' });
        }
      } else if (spaceBelow < dropdownHeight) {
        // Scroll down to make room for dropdown below
        const scrollAmount = dropdownHeight - spaceBelow + 20;
        window.scrollBy({ top: scrollAmount, behavior: 'smooth' });
      }
    }
  }, [isOpen]);

  // Manejar selección de opción
  const handleSelect = (option: string) => {
    onChange(option);
    setIsOpen(false);
    setSearchTerm('');
  };

  // Manejar adición de nuevo valor
  const handleAddNew = async () => {
    if (!newValue.trim() || !onAddNew) return;

    setIsAdding(true);
    try {
      const success = await onAddNew(newValue.trim());
      if (success) {
        onChange(newValue.trim());
        setIsOpen(false);
        setIsEditing(false);
        setNewValue('');
      }
    } catch (error) {
      console.error('Error adding new value:', error);
    } finally {
      setIsAdding(false);
    }
  };

  // Validar si el valor actual es válido
  const isValidValue = !strict || options.includes(value) || value === '';

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      <Button
        type='button'
        variant='outline'
        className={`w-full justify-between ${!isValidValue ? 'border-red-500 text-red-600' : ''}`}
        onClick={() => setIsOpen(!isOpen)}
      >
        <span className='truncate text-left'>{value || placeholder}</span>
        <ChevronDown className='h-4 w-4 flex-shrink-0' />
      </Button>

      {isOpen && (
        <div 
          className='absolute z-[9999] w-full mt-1 bg-white border border-gray-200 rounded-md shadow-lg'
          style={(() => {
            const triggerRect = dropdownRef.current?.getBoundingClientRect();
            const dropdownHeight = 300; // Approximate max height of dropdown
            const viewportHeight = window.innerHeight;
            const spaceBelow = viewportHeight - (triggerRect?.bottom || 0);
            const spaceAbove = triggerRect?.top || 0;
            
            // Position above if not enough space below and more space above
            const shouldPositionAbove = spaceBelow < dropdownHeight && spaceAbove > spaceBelow;
            
            return {
              position: 'fixed',
              zIndex: 9999,
              width: triggerRect?.width || 'auto',
              top: shouldPositionAbove 
                ? (triggerRect?.top || 0) - dropdownHeight - 4
                : (triggerRect?.bottom || 0) + 4,
              left: triggerRect?.left || 0,
              maxHeight: shouldPositionAbove 
                ? Math.min(dropdownHeight, spaceAbove - 20)
                : Math.min(dropdownHeight, spaceBelow - 20),
            };
          })()}
        >
          {/* Buscador */}
          <div className='p-2 border-b'>
            <Input
              placeholder='Search...'
              value={searchTerm}
              onChange={e => setSearchTerm(e.target.value)}
              className='h-8'
            />
          </div>

          {/* Lista de opciones */}
          <div className='max-h-60 overflow-y-auto'>
            {filteredOptions.length > 0 ? (
              filteredOptions.map(option => (
                <button
                  key={option}
                  className='w-full px-3 py-2 text-left hover:bg-gray-100 flex items-center justify-between'
                  onClick={() => handleSelect(option)}
                >
                  <span>{option}</span>
                  {value === option && (
                    <Check className='h-4 w-4 text-green-600' />
                  )}
                </button>
              ))
            ) : (
              <div className='px-3 py-2 text-gray-500 text-sm'>
                No options found
              </div>
            )}

            {/* Opción para agregar nuevo valor */}
            {allowNew &&
              searchTerm &&
              !options.some(
                opt => opt.toLowerCase() === searchTerm.toLowerCase()
              ) && (
                <div className='border-t'>
                  <button
                    className='w-full px-3 py-2 text-left hover:bg-gray-100 flex items-center text-blue-600'
                    onClick={() => {
                      setIsEditing(true);
                      setNewValue(searchTerm);
                    }}
                  >
                    <Plus className='h-4 w-4 mr-2' />
                    Add "{searchTerm}"
                  </button>
                </div>
              )}

            {/* Formulario para agregar nuevo valor */}
            {allowNew && !isEditing && (
              <div className='border-t'>
                <button
                  className='w-full px-3 py-2 text-left hover:bg-gray-100 flex items-center text-blue-600'
                  onClick={() => {
                    setIsEditing(true);
                    setNewValue('');
                  }}
                >
                  <Plus className='h-4 w-4 mr-2' />
                  Add new value...
                </button>
              </div>
            )}
          </div>

          {/* Editor para nuevo valor */}
          {isEditing && (
            <div className='border-t p-2 bg-gray-50'>
              <div className='flex space-x-2'>
                <Input
                  placeholder='Enter new value...'
                  value={newValue}
                  onChange={e => setNewValue(e.target.value)}
                  className='h-8 flex-1'
                  onKeyDown={e => {
                    if (e.key === 'Enter') {
                      e.preventDefault();
                      handleAddNew();
                    } else if (e.key === 'Escape') {
                      setIsEditing(false);
                      setNewValue('');
                    }
                  }}
                  autoFocus
                />
                <Button
                  size='sm'
                  onClick={handleAddNew}
                  disabled={!newValue.trim() || isAdding}
                  className='h-8 px-3'
                >
                  {isAdding ? '...' : 'Add'}
                </Button>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Mensaje de error para valores inválidos */}
      {!isValidValue && (
        <div className='text-red-500 text-xs mt-1'>
          Please select a valid option from the list
        </div>
      )}
    </div>
  );
}
