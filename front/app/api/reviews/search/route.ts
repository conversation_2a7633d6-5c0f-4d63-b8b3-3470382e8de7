import { NextRequest, NextResponse } from 'next/server';
import { reviewService } from '@/lib/reviewService';

export async function GET(request: NextRequest) {
  try {
    await reviewService.initialize();
    
    const { searchParams } = new URL(request.url);
    const softwareName = searchParams.get('software_name');
    const category = searchParams.get('category');
    const commercialName = searchParams.get('commercial_name');
    const tenantId = searchParams.get('tenant_id');

    let results;

    if (softwareName) {
      results = await reviewService.searchBySoftware(softwareName, tenantId || undefined);
    } else if (category) {
      results = await reviewService.searchByCategory(category, tenantId || undefined);
    } else if (commercialName) {
      results = await reviewService.searchByCommercialName(commercialName, tenantId || undefined);
    } else {
      return NextResponse.json(
        { error: 'Se requiere software_name, category o commercial_name para buscar' }, 
        { status: 400 }
      );
    }

    return NextResponse.json({ 
      success: true, 
      results,
      total: results.correct.length + results.edited.length
    });
  } catch (error) {
    console.error('Error buscando reviews:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' }, 
      { status: 500 }
    );
  }
}
