import { NextRequest, NextResponse } from 'next/server';
import { reviewService } from '@/lib/reviewService';
import { reviewEventEmitter } from '@/lib/reviewEventEmitter';

export async function POST(request: NextRequest) {
  try {
    await reviewService.initialize();
    
    const body = await request.json();
    const { 
      software_name, 
      category, 
      commercial_name,
      confidence_score, 
      reviewer, 
      tenant_id,
      notes 
    } = body;

    // Validar datos requeridos
    if (!software_name || !category) {
      return NextResponse.json(
        { error: 'software_name y category son requeridos' }, 
        { status: 400 }
      );
    }

    const reviewId = await reviewService.addReviewCorrect({
      software_name,
      category,
      commercial_name,
      confidence_score,
      reviewer,
      tenant_id,
      notes
    });

    // Emitir evento SSE
    reviewEventEmitter.emitReviewAdded({
      type: 'correct',
      itemId: `${tenant_id}-${software_name}`,
      tenant_id: tenant_id || 'unknown',
      software_name,
      feedback: 'positive',
      timestamp: Date.now()
    });
    
    return NextResponse.json({ 
      success: true, 
      reviewId,
      message: 'Review correcto agregado a review_correct' 
    });
  } catch (error) {
    console.error('Error agregando review correcto:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' }, 
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    await reviewService.initialize();
    
    const { searchParams } = new URL(request.url);
    const tenantId = searchParams.get('tenant_id');
    
    const reviews = await reviewService.getReviewsCorrect(tenantId || undefined);
    
    return NextResponse.json({ 
      success: true, 
      reviews,
      count: reviews.length 
    });
  } catch (error) {
    console.error('Error obteniendo reviews correctos:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' }, 
      { status: 500 }
    );
  }
}