import { NextRequest, NextResponse } from 'next/server';
import { reviewService } from '@/lib/reviewService';

export async function GET(request: NextRequest) {
  try {
    await reviewService.initialize();
    
    const { searchParams } = new URL(request.url);
    const tenantId = searchParams.get('tenant_id');
    
    const trends = await reviewService.getCorrectionTrends(tenantId || undefined);
    
    return NextResponse.json({ 
      success: true, 
      trends 
    });
  } catch (error) {
    console.error('Error obteniendo tendencias:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' }, 
      { status: 500 }
    );
  }
}
