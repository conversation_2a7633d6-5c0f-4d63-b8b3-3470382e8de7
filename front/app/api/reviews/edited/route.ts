import { NextRequest, NextResponse } from 'next/server';
import { reviewService } from '@/lib/reviewService';
import { reviewEventEmitter } from '@/lib/reviewEventEmitter';

export async function POST(request: NextRequest) {
  try {
    await reviewService.initialize();
    
    const body = await request.json();
    const { 
      software_name,
      original_category,
      original_commercial_name,
      corrected_category,
      corrected_commercial_name,
      confidence_score, 
      reviewer, 
      tenant_id,
      notes 
    } = body;

    // Validar datos requeridos
    if (!software_name || !original_category || !corrected_category) {
      return NextResponse.json(
        { error: 'software_name, original_category y corrected_category son requeridos' }, 
        { status: 400 }
      );
    }

    const reviewId = await reviewService.addReviewEdited({
      software_name,
      original_category,
      original_commercial_name,
      corrected_category,
      corrected_commercial_name,
      confidence_score,
      reviewer,
      tenant_id,
      notes
    });

    // Emitir evento SSE
    reviewEventEmitter.emitReviewAdded({
      type: 'edited',
      itemId: `${tenant_id}-${software_name}`,
      tenant_id: tenant_id || 'unknown',
      software_name,
      feedback: 'corrected',
      timestamp: Date.now()
    });
    
    return NextResponse.json({ 
      success: true, 
      reviewId,
      message: 'Review editado agregado a review_edited' 
    });
  } catch (error) {
    console.error('Error agregando review editado:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' }, 
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    await reviewService.initialize();
    
    const { searchParams } = new URL(request.url);
    const tenantId = searchParams.get('tenant_id');
    
    const reviews = await reviewService.getReviewsEdited(tenantId || undefined);
    
    return NextResponse.json({ 
      success: true, 
      reviews,
      count: reviews.length 
    });
  } catch (error) {
    console.error('Error obteniendo reviews editados:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' }, 
      { status: 500 }
    );
  }
}
