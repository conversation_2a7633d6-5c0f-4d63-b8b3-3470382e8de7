import { type NextRequest, NextResponse } from 'next/server';
import { readTenantDataFromS3 } from '@/lib/s3-utils';
import { requireAuth } from '@/lib/auth-utils';
import { ReviewService } from '@/lib/reviewService';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ tenantId: string }> }
) {
  // Check authentication
  const authError = await requireAuth(request);
  if (authError) {
    return authError;
  }
  try {
    const { tenantId } = await params;
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const filter = searchParams.get('filter') || 'all';
    const commercialNameFilter = searchParams.get('commercialNameFilter') || 'all';
    const categoryFilter = searchParams.get('categoryFilter') || 'all';
    const offset = (page - 1) * limit;

    try {
      // Leer el archivo JSON del tenant desde S3
      const tenantData = await readTenantDataFromS3(tenantId);

      // Extraer todas las aplicaciones de todos los registros
      const allApplications: any[] = [];

      if (Array.isArray(tenantData)) {
        tenantData.forEach((record: any) => {
          if (record._source && Array.isArray(record._source.Applications)) {
            record._source.Applications.forEach((app: any) => {
              allApplications.push({
                id: `${record._id}_${app.IdentifyingNumber || Math.random()}`,
                name: app.Name,
                provider: app.Vendor || 'Unknown',
                version: app.Version,
                installDate: app.InstallDate,
                category: app.Category || app.category || null,
                commercialName: app.CommercialName || app.ComercialName || null,
                feedback: null,
              });
            });
          }
        });
      }

      // Eliminar duplicados basado en Name y Vendor
      const uniqueApplications = allApplications.reduce(
        (acc: any[], current) => {
          const isDuplicate = acc.some(
            app =>
              app.name === current.name && app.provider === current.provider
          );
          if (!isDuplicate) {
            acc.push(current);
          }
          return acc;
        },
        []
      );

      // Leer datos de feedback desde MongoDB usando ReviewService
      const reviewService = new ReviewService();
      await reviewService.initialize();
      
      let feedbackData: { correct: any[], edited: any[] } = { correct: [], edited: [] };
      try {
        console.log(`🔄 Obteniendo reviews de MongoDB para tenant: ${tenantId}`);
        
        const [correctReviews, editedReviews] = await Promise.all([
          reviewService.getReviewsCorrect(tenantId),
          reviewService.getReviewsEdited(tenantId),
        ]);

        feedbackData.correct = correctReviews;
        feedbackData.edited = editedReviews;
        
        console.log(`✅ Reviews obtenidos de MongoDB - Correctos: ${correctReviews.length}, Editados: ${editedReviews.length}`);
      } catch (feedbackError) {
        console.error('Error reading feedback data from MongoDB:', feedbackError);
      }

      // Aplicar feedback a los items basado en name y provider matching
      const itemsWithFeedback = uniqueApplications.map(item => {
        // Buscar feedback correcto por software_name que coincida con el name del item
        const correctFeedback = feedbackData.correct.find(
          (f: any) => f.software_name.toLowerCase() === item.name.toLowerCase()
        );
        
        // Buscar feedback editado por software_name que coincida con el name del item
        const editedFeedback = feedbackData.edited.find(
          (f: any) => f.software_name.toLowerCase() === item.name.toLowerCase()
        );

        if (editedFeedback) {
          return {
            ...item,
            feedback: 'corrected',
            commercialName: editedFeedback.corrected_commercial_name || item.commercialName,
            category: editedFeedback.corrected_category || item.category,
          };
        } else if (correctFeedback) {
          return {
            ...item,
            feedback: 'positive',
          };
        }

        return item;
      });

      // Aplicar filtros comerciales primero (Commercial Name y Category)
      let commerciallyFilteredItems = itemsWithFeedback.filter(item => {
        // Commercial Name filter - case insensitive and null-safe
        const commercialNameMatch =
          commercialNameFilter === 'all' ||
          (item.commercialName && commercialNameFilter && 
           item.commercialName.toLowerCase().trim() === commercialNameFilter.toLowerCase().trim());
        
        // Category filter - case insensitive and null-safe
        const categoryMatch =
          categoryFilter === 'all' || 
          (item.category && categoryFilter && 
           item.category.toLowerCase().trim() === categoryFilter.toLowerCase().trim());

        return commercialNameMatch && categoryMatch;
      });

      // Aplicar filtro de estado (status) DESPUÉS de los filtros comerciales
      let filteredItems = commerciallyFilteredItems;
      if (filter === 'positive') {
        filteredItems = commerciallyFilteredItems.filter(
          item => item.feedback === 'positive'
        );
      } else if (filter === 'negative') {
        filteredItems = commerciallyFilteredItems.filter(
          item => item.feedback === 'corrected'
        );
      } else if (filter === 'not-reviewed') {
        filteredItems = commerciallyFilteredItems.filter(
          item => item.feedback === null
        );
      }

      // Aplicar paginación DESPUÉS del filtrado
      const paginatedApps = filteredItems.slice(offset, offset + limit);

      const response = {
        data: paginatedApps,
        pagination: {
          currentPage: page,
          totalItems: filteredItems.length,
          totalPages: Math.ceil(filteredItems.length / limit),
          itemsPerPage: limit,
          hasNextPage: offset + limit < filteredItems.length,
          hasPreviousPage: page > 1,
        },
        totalCounts: {
          all: itemsWithFeedback.length,
          positive: feedbackData.correct.length,
          edited: feedbackData.edited.length,
          notReviewed:
            itemsWithFeedback.length -
            feedbackData.correct.length -
            feedbackData.edited.length,
        },
        filteredCounts: {
          all: commerciallyFilteredItems.length,
          positive: commerciallyFilteredItems.filter(item => item.feedback === 'positive').length,
          edited: commerciallyFilteredItems.filter(item => item.feedback === 'corrected').length,
          notReviewed: commerciallyFilteredItems.filter(item => item.feedback === null).length,
        },
      };

      return NextResponse.json(response);
    } catch (fileError) {
      // Si el archivo no existe, devolver array vacío o error específico
      console.error(`File not found for tenant ${tenantId}:`, fileError);
      return NextResponse.json(
        { error: `Tenant ${tenantId} not found` },
        { status: 404 }
      );
    }
  } catch (error) {
    console.error('Error fetching software inventory:', error);
    return NextResponse.json(
      { error: 'Failed to fetch software inventory' },
      { status: 500 }
    );
  }
}
