import { NextRequest, NextResponse } from 'next/server';
import mongoose from 'mongoose';

export async function GET(request: NextRequest) {
  try {
    console.log('🔍 Iniciando diagnóstico de MongoDB con autenticación por certificado...');
    
    const uri = process.env.MONGO_URI;
    const cert = process.env.DB_CERT;
    const certRequired = process.env.DB_CERT_REQUIRED === 'true';

    // Validaciones básicas
    if (!uri) {
      return NextResponse.json({
        success: false,
        error: 'MONGO_URI no está definida en las variables de entorno'
      }, { status: 500 });
    }

    if (certRequired && !cert) {
      return NextResponse.json({
        success: false,
        error: 'DB_CERT no está definida pero DB_CERT_REQUIRED=true'
      }, { status: 500 });
    }

    // Verificar formato de URI (NO debe tener usuario:password para certificados)
    if (uri.includes('@') && uri.match(/:\/\/([^:]+):([^@]+)@/)) {
      return NextResponse.json({
        success: false,
        error: 'MONGO_URI contiene credenciales usuario:password. Para autenticación por certificado NO deben incluirse.'
      }, { status: 500 });
    }

    console.log('✅ Variables de entorno validadas');
    console.log('URI format:', uri.substring(0, 50) + '...');
    console.log('Certificate length:', cert?.length || 0);
    console.log('Cert required:', certRequired);

    const dbConnectOptions: any = {};

    if (certRequired && cert) {
      // Crear archivo temporal del certificado
      const fs = require('fs');
      const path = require('path');
      const certPath = path.join(process.cwd(), 'debug-mongoose-cert.pem');
      fs.writeFileSync(certPath, cert);

      console.log('✅ Certificado escrito exitosamente');

      // Configuración para autenticación X.509
      dbConnectOptions.ssl = true;
      dbConnectOptions.tlsAllowInvalidCertificates = false;
      dbConnectOptions.tlsCertificateKeyFile = certPath;
      dbConnectOptions.maxPoolSize = 10;
      dbConnectOptions.authMechanism = 'MONGODB-X509';
      dbConnectOptions.authSource = '$external';
      
      console.log('🔐 Configuración X.509 aplicada');
    }

    console.log('🔗 Intentando conectar con Mongoose (X.509)...');
    
    // Conectar con Mongoose
    await mongoose.connect(uri, dbConnectOptions);
    
    console.log('✅ Conectado exitosamente con autenticación por certificado');

    // Probar operaciones de base de datos
    const db = mongoose.connection.db;
    const collections = await db.listCollections().toArray();
    const dbStats = await db.stats();

    console.log('✅ Operaciones de base de datos exitosas');

    // Limpiar archivo de certificado
    if (certRequired && cert) {
      const fs = require('fs');
      const path = require('path');
      const certPath = path.join(process.cwd(), 'debug-mongoose-cert.pem');
      fs.unlinkSync(certPath);
    }

    // Desconectar
    await mongoose.disconnect();

    return NextResponse.json({
      success: true,
      message: '✅ ¡Autenticación por certificado X.509 exitosa!',
      details: {
        database: 'software-classifier',
        authMethod: 'X.509 Certificate',
        collections: collections.map(c => c.name),
        stats: {
          collections: dbStats.collections,
          dataSize: dbStats.dataSize,
          storageSize: dbStats.storageSize
        },
        mongooseVersion: mongoose.version,
        uriFormat: 'Sin credenciales (correcto para X.509)'
      }
    });
    
  } catch (error: any) {
    console.error('❌ Error en diagnóstico:', error);
    
    // Limpiar archivo de certificado si existe
    try {
      const fs = require('fs');
      const path = require('path');
      const certPath = path.join(process.cwd(), 'debug-mongoose-cert.pem');
      if (fs.existsSync(certPath)) {
        fs.unlinkSync(certPath);
      }
    } catch {}

    return NextResponse.json({
      success: false,
      error: error.message || 'Error desconocido',
      details: {
        code: error.code,
        codeName: error.codeName,
        name: error.name,
        authMethod: 'X.509 Certificate',
        suggestion: error.code === 18 ? 'Problema de autenticación - verificar certificado' : 'Error de conexión'
      }
    }, { status: 500 });
  }
}
