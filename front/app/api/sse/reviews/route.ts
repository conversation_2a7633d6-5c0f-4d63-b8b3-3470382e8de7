import { NextRequest } from 'next/server';
import { reviewEventEmitter } from '@/lib/reviewEventEmitter';

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const tenantId = searchParams.get('tenant_id');

  // Crear stream SSE
  const stream = new ReadableStream({
    start(controller) {
      // Función para enviar eventos SSE
      const sendEvent = (data: any) => {
        const eventData = `data: ${JSON.stringify(data)}\n\n`;
        controller.enqueue(new TextEncoder().encode(eventData));
      };

      // Enviar evento inicial de conexión
      sendEvent({ type: 'connected', timestamp: Date.now() });

      // Escuchar eventos de reviews
      const handleReviewEvent = (eventData: any) => {
        // Filtrar por tenant_id si se especifica
        if (tenantId && eventData.tenant_id !== tenantId) {
          return;
        }
        sendEvent(eventData);
      };

      // Registrar listeners
      reviewEventEmitter.on('review_added', handleReviewEvent);
      reviewEventEmitter.on('review_updated', handleReviewEvent);

      // Cleanup cuando se cierra la conexión
      request.signal.addEventListener('abort', () => {
        reviewEventEmitter.off('review_added', handleReviewEvent);
        reviewEventEmitter.off('review_updated', handleReviewEvent);
        controller.close();
      });
    }
  });

  return new Response(stream, {
    headers: {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Cache-Control'
    }
  });
}