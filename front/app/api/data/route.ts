import { NextRequest, NextResponse } from 'next/server';
import {
  readFromS3,
  writeCommercialNamesAdditionsToS3,
  readCommercialNamesAdditionsFromS3,
} from '@/lib/s3-utils';
import { requireAuth } from '@/lib/auth-utils';

export async function GET(request: NextRequest) {
  // Check authentication
  const authError = await requireAuth(request);
  if (authError) {
    return authError;
  }
  try {
    // Leer archivos de listas desde S3
    const [commercialNamesData, categoriesData, additionsData] =
      await Promise.all([
        readFromS3('commercial_name_list.json'),
        readFromS3('category_list.json'),
        readCommercialNamesAdditionsFromS3(),
      ]);

    // Combinar nombres comerciales principales con adiciones y eliminar duplicados
    const allCommercialNames = [...new Set([
      ...commercialNamesData.commercialNames,
      ...additionsData.commercialNames,
    ])].sort();

    return NextResponse.json({
      commercialNames: allCommercialNames,
      categories: categoriesData.categories.sort(),
    });
  } catch (error) {
    console.error('Error fetching data lists:', error);
    return NextResponse.json(
      { error: 'Failed to fetch data lists' },
      { status: 500 }
    );
  }
}

export async function POST(request: Request) {
  try {
    const { type, value } = await request.json();

    if (!type || !value) {
      return NextResponse.json(
        { error: 'Missing type or value' },
        { status: 400 }
      );
    }

    if (type === 'commercialName') {
      // Agregar nuevo nombre comercial a additions desde S3
      const additionsData = await readCommercialNamesAdditionsFromS3();

      // Verificar si ya existe
      if (!additionsData.commercialNames.includes(value)) {
        additionsData.commercialNames.push(value);
        additionsData.commercialNames.sort();

        await writeCommercialNamesAdditionsToS3(additionsData);
      }

      return NextResponse.json({
        success: true,
        message: 'Commercial name added successfully',
      });
    } else {
      return NextResponse.json({ error: 'Invalid type' }, { status: 400 });
    }
  } catch (error) {
    console.error('Error adding new value:', error);
    return NextResponse.json(
      { error: 'Failed to add new value' },
      { status: 500 }
    );
  }
}
