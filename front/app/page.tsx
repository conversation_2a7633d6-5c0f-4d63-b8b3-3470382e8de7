'use client';

import { useState, useEffect, useRef } from 'react';
import {
  ChevronDown,
  ThumbsUp,
  ThumbsDown,
  Check,
  X,
  CheckCircle,
  ChevronLeft,
  ChevronRight,
  LogOut,
  Loader2,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { NavigableDropdown } from '@/components/ui/navigable-dropdown';
import { useAuth } from '@/lib/auth-context';
import { AuthLoading } from '@/components/auth-loading';
import { useReviews } from '@/hooks/useReviews';
import { useSSE } from '@/hooks/useSSE';
import Image from 'next/image';

interface SoftwareItem {
  id: string;
  name: string;
  provider: string;
  version?: string;
  commercialName?: string;
  category?: string;
  feedback?: 'positive' | 'negative' | 'corrected' | null;
}

interface EditingItem {
  id: string;
  commercialName: string;
  category: string;
}

interface Tenant {
  id: string;
  name: string;
}

// Special constant for "All Tenants" option
const ALL_TENANTS_OPTION: Tenant = {
  id: 'all-tenants',
  name: 'All Tenants'
};

interface PaginationData {
  currentPage: number;
  totalItems: number;
  totalPages: number;
  itemsPerPage: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

interface TotalCounts {
  all: number;
  positive: number;
  edited: number;
  notReviewed: number;
}

interface DataLists {
  commercialNames: string[];
  categories: string[];
}

export default function SoftwareClassifier() {
  const { logout, isLoading: authLoading } = useAuth();
  const [selectedTenant, setSelectedTenant] = useState<Tenant | null>(null);

    // Inicializar hook de reviews con el tenant seleccionado
  // Si es "All Tenants", pasamos undefined para obtener stats globales
  const { 
    addReviewCorrect, 
    addReviewEdited, 
    stats: reviewStats, 
    loading: reviewsLoading,
    error: reviewsError 
  } = useReviews(selectedTenant?.id === 'all-tenants' ? undefined : selectedTenant?.id);

  // SSE para actualizaciones en tiempo real
  const { connected: sseConnected, lastEvent } = useSSE(
    selectedTenant?.id === 'all-tenants' ? undefined : selectedTenant?.id
  );
  const [tenants, setTenants] = useState<Tenant[]>([]);
  const [softwareItems, setSoftwareItems] = useState<SoftwareItem[]>([]);
  const [pagination, setPagination] = useState<PaginationData | null>(null);
  const [totalCounts, setTotalCounts] = useState<TotalCounts>({
    all: 0,
    positive: 0,
    edited: 0,
    notReviewed: 0,
  });
  const [apiFilteredCounts, setApiFilteredCounts] = useState<TotalCounts>({
    all: 0,
    positive: 0,
    edited: 0,
    notReviewed: 0,
  });
  const [loading, setLoading] = useState(false);
  const [tenantsLoading, setTenantsLoading] = useState(true);
  const currentRequestRef = useRef<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [filter, setFilter] = useState<
    'all' | 'positive' | 'negative' | 'not-reviewed'
  >('all');
  const [editingItem, setEditingItem] = useState<EditingItem | null>(null);

  // Filter states
  const [commercialNameFilter, setCommercialNameFilter] =
    useState<string>('all');
  const [categoryFilter, setCategoryFilter] = useState<string>('all');

  // Data lists
  const [dataLists, setDataLists] = useState<DataLists>({
    commercialNames: [],
    categories: [],
  });
  const [dataListsLoading, setDataListsLoading] = useState(true);

  // Error states
  const [editError, setEditError] = useState<string>('');

  // Optimistic updates state
  const [pendingOperations, setPendingOperations] = useState<Set<string>>(new Set());
  const [successMessage, setSuccessMessage] = useState<string>('');

  // SSE status indicator
  const [showSSEStatus, setShowSSEStatus] = useState(false);

  // Helper function to calculate statistics from local state
  const calculateLocalStats = () => {
    const positive = softwareItems.filter(item => item.feedback === 'positive').length;
    const edited = softwareItems.filter(item => item.feedback === 'corrected').length;
    const notReviewed = softwareItems.filter(item => !item.feedback || item.feedback === 'negative').length;
    const total = softwareItems.length;

    return {
      positive,
      edited,
      notReviewed,
      all: total
    };
  };

  // Helper function to update item in state
  const updateItemInState = (itemId: string, updates: Partial<SoftwareItem>) => {
    setSoftwareItems(prevItems =>
      prevItems.map(item =>
        item.id === itemId ? { ...item, ...updates } : item
      )
    );
  };

  // Helper function to show success message briefly
  const showSuccessMessage = (message: string) => {
    setSuccessMessage(message);
    setTimeout(() => setSuccessMessage(''), 3000);
  };

  // Fetch data lists
  const fetchDataLists = async () => {
    setDataListsLoading(true);
    try {
      const response = await fetch('/api/data');
      if (response.ok) {
        const data = await response.json();
        setDataLists(data);
      } else {
        console.error('Error fetching data lists:', response.statusText);
      }
    } catch (error) {
      console.error('Error fetching data lists:', error);
    } finally {
      setDataListsLoading(false);
    }
  };

  // Add new commercial name
  const addNewCommercialName = async (value: string): Promise<boolean> => {
    try {
      const response = await fetch('/api/data', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type: 'commercialName',
          value: value,
        }),
      });

      if (response.ok) {
        // Refresh data lists
        await fetchDataLists();
        return true;
      }
      return false;
    } catch (error) {
      console.error('Error adding commercial name:', error);
      return false;
    }
  };

  // Fetch tenants list
  const fetchTenants = async () => {
    setTenantsLoading(true);
    try {
      const response = await fetch('/api/tenants');
      if (response.ok) {
        const data = await response.json();
        setTenants(data);
        // No auto-select any tenant - let user choose
      } else {
        console.error('Error fetching tenants:', response.statusText);
      }
    } catch (error) {
      console.error('Error fetching tenants:', error);
    } finally {
      setTenantsLoading(false);
    }
  };

  // Fetch software inventory for selected tenant
  const fetchSoftwareInventory = async (
    tenantId: string,
    page: number = 1,
    currentFilter: string = 'all'
  ) => {
    // Prevent race conditions by tracking which tenant is being loaded
    currentRequestRef.current = tenantId;
    setLoading(true);

    try {
      // Build URL with current filters
      const params = new URLSearchParams({
        page: page.toString(),
        limit: '10',
        filter: currentFilter,
        commercialNameFilter: commercialNameFilter,
        categoryFilter: categoryFilter,
      });

      const response = await fetch(
        `/api/tenants/${tenantId}/software-inventory?${params}`
      );

      // Check if this is still the current request (race condition protection)
      if (currentRequestRef.current !== tenantId) {
        console.log(`🚫 Ignoring response for tenant ${tenantId} - newer request in progress`);
        return;
      }

      if (response.ok) {
        const data = await response.json();

        // Double-check this is still the selected tenant
        if (selectedTenant?.id === tenantId && currentRequestRef.current === tenantId) {
          setSoftwareItems(data.data);
          setPagination(data.pagination);
          setTotalCounts(data.totalCounts);
          setApiFilteredCounts(data.filteredCounts || data.totalCounts);

          // Categories loaded successfully
        }
      } else {
        console.error(
          'Error fetching software inventory:',
          response.statusText
        );
        if (selectedTenant?.id === tenantId && currentRequestRef.current === tenantId) {
          setSoftwareItems([]);
          setPagination(null);
          setTotalCounts({ all: 0, positive: 0, edited: 0, notReviewed: 0 });
          setApiFilteredCounts({ all: 0, positive: 0, edited: 0, notReviewed: 0 });
        }
      }
    } catch (error) {
      console.error('Error fetching software inventory:', error);
      if (selectedTenant?.id === tenantId && currentRequestRef.current === tenantId) {
        setSoftwareItems([]);
        setPagination(null);
        setTotalCounts({ all: 0, positive: 0, edited: 0, notReviewed: 0 });
        setApiFilteredCounts({ all: 0, positive: 0, edited: 0, notReviewed: 0 });
      }
    } finally {
      // Only clear loading if this was the current request
      if (currentRequestRef.current === tenantId) {
        setLoading(false);
        currentRequestRef.current = null;
      }
    }
  };

  // Save feedback to MongoDB (with optimistic updates)
  const saveFeedback = async (
    type: 'correct' | 'edited',
    itemId: string,
    itemData: any,
    editedData?: any
  ) => {
    // Add to pending operations
    setPendingOperations(prev => new Set(prev).add(itemId));

    try {
      if (type === 'correct') {
        // Agregar review correcto a MongoDB
        const success = await addReviewCorrect({
          software_name: itemData.name,
          category: itemData.category || 'Uncategorized',
          commercial_name: itemData.commercialName,
          confidence_score: itemData.confidence_score || 0.8,
          notes: `Tenant: ${selectedTenant?.name}`
        });

        if (success) {
          console.log('✅ Review correcto guardado en MongoDB collection: review_correct');
          // Remove from pending operations
          setPendingOperations(prev => {
            const newSet = new Set(prev);
            newSet.delete(itemId);
            return newSet;
          });
          return true;
        } else {
          throw new Error('Failed to save correct review');
        }
      } else if (type === 'edited' && editedData) {
        // Agregar review editado a MongoDB
        const success = await addReviewEdited({
          software_name: itemData.name,
          original_category: itemData.category || 'Uncategorized',
          original_commercial_name: itemData.commercialName,
          corrected_category: editedData.category,
          corrected_commercial_name: editedData.commercialName,
          confidence_score: itemData.confidence_score || 0.8,
          notes: `Tenant: ${selectedTenant?.name} - Original category: ${itemData.category}, Original commercial name: ${itemData.commercialName}`
        });

        if (success) {
          console.log('✅ Review editado guardado en MongoDB collection: review_edited');
          // Remove from pending operations
          setPendingOperations(prev => {
            const newSet = new Set(prev);
            newSet.delete(itemId);
            return newSet;
          });
          return true;
        } else {
          throw new Error('Failed to save edited review');
        }
      }
    } catch (error) {
      console.error('Error saving feedback to MongoDB:', error);
      // Remove from pending operations on error
      setPendingOperations(prev => {
        const newSet = new Set(prev);
        newSet.delete(itemId);
        return newSet;
      });

      // Show error to user
      setEditError('Failed to save review. Please try again.');
      setTimeout(() => setEditError(''), 3000);
    }
    return false;
  };

  // Remove feedback from MongoDB (with optimistic updates)
  const removeFeedback = async (type: 'correct' | 'edited', itemId: string) => {
    // Add to pending operations
    setPendingOperations(prev => new Set(prev).add(itemId));

    try {
      // For now, we'll just simulate the removal
      // TODO: Implement proper delete functionality when needed
      console.log(`🔄 Remove feedback requested for ${type} review of item ${itemId}`);

      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 500));

      // Remove from pending operations
      setPendingOperations(prev => {
        const newSet = new Set(prev);
        newSet.delete(itemId);
        return newSet;
      });

      return true;
    } catch (error) {
      console.error('Error removing feedback from MongoDB:', error);

      // Remove from pending operations on error
      setPendingOperations(prev => {
        const newSet = new Set(prev);
        newSet.delete(itemId);
        return newSet;
      });

      // Show error to user
      setEditError('Failed to remove review. Please try again.');
      setTimeout(() => setEditError(''), 3000);
    }
    return false;
  };

  // Handle tenant selection
  const handleTenantSelect = (tenant: Tenant) => {
    setSelectedTenant(tenant);
    setCurrentPage(1);
    setFilter('all');
    setCommercialNameFilter('all');
    setCategoryFilter('all');
    setTotalCounts({ all: 0, positive: 0, edited: 0, notReviewed: 0 });
  };

  // Handle page change
  const handlePageChange = (newPage: number) => {
    setCurrentPage(newPage);
  };

  // Handle filter change
  const handleFilterChange = (
    newFilter: 'all' | 'positive' | 'negative' | 'not-reviewed'
  ) => {
    if (filter === newFilter) {
      setFilter('all');
      setCurrentPage(1);
    } else {
      setFilter(newFilter);
      setCurrentPage(1); // Reset to first page when changing filter
    }
  };

  // Handle feedback interaction (with optimistic updates)
  const handleFeedback = async (
    itemId: string,
    feedbackType: 'positive' | 'negative'
  ) => {
    const item = softwareItems.find(item => item.id === itemId);
    if (!item) return;

    if (feedbackType === 'negative') {
      // Start editing mode for thumbs down
      setEditingItem({
        id: itemId,
        commercialName: item.commercialName || '',
        category: item.category || '',
      });
      setEditError(''); // Clear any previous errors
    } else {
      // Handle positive feedback - optimistic update
      if (item.feedback === 'positive') {
        // Optimistically remove positive feedback
        updateItemInState(itemId, { feedback: null });

        // Try to remove from MongoDB
        const success = await removeFeedback('correct', itemId);
        if (!success) {
          // Rollback on failure
          updateItemInState(itemId, { feedback: 'positive' });
        } else {
          showSuccessMessage('Review removed successfully!');
        }
      } else {
        // Optimistically add positive feedback
        updateItemInState(itemId, { feedback: 'positive' });

        // Try to save to MongoDB
        const success = await saveFeedback('correct', itemId, {
          name: item.name,
          provider: item.provider,
          version: item.version,
          category: item.category,
          commercialName: item.commercialName,
          confidence_score: 0.85 // Default confidence for user feedback
        });

        if (!success) {
          // Rollback on failure
          updateItemInState(itemId, { feedback: item.feedback });
        } else {
          console.log('🚀 Thumbs up guardado en MongoDB!');
          showSuccessMessage('Review saved successfully!');
        }
      }
    }
  };

  // Handle editing field changes
  const handleEditChange = (
    field: 'commercialName' | 'category',
    value: string
  ) => {
    if (editingItem) {
      setEditingItem({
        ...editingItem,
        [field]: value,
      });

      // Clear error when user starts typing
      if (editError) {
        setEditError('');
      }
    }
  };

  // Confirm editing changes (with optimistic updates)
  const confirmEdit = async () => {
    if (!editingItem) return;

    // Validate category is from the allowed list
    if (
      editingItem.category &&
      !dataLists.categories.includes(editingItem.category)
    ) {
      setEditError('Please select a valid category from the list');
      return;
    }

    const item = softwareItems.find(item => item.id === editingItem.id);
    if (!item) return;

    // Store original values for potential rollback
    const originalData = {
      commercialName: item.commercialName,
      category: item.category,
      feedback: item.feedback
    };

    // Optimistically update the item
    updateItemInState(editingItem.id, {
      commercialName: editingItem.commercialName,
      category: editingItem.category,
      feedback: 'corrected'
    });

    // Reset editing state immediately for better UX
    setEditingItem(null);
    setEditError('');

    // Try to save to MongoDB
    const success = await saveFeedback(
      'edited',
      editingItem.id,
      {
        name: item.name,
        provider: item.provider,
        version: item.version,
        category: item.category,
        commercialName: item.commercialName,
        confidence_score: 0.75 // Lower confidence for edited items
      },
      {
        category: editingItem.category,
        commercialName: editingItem.commercialName,
      }
    );

    if (!success) {
      // Rollback on failure
      updateItemInState(editingItem.id, originalData);
      // Re-enter editing mode on failure
      setEditingItem({
        id: editingItem.id,
        commercialName: editingItem.commercialName,
        category: editingItem.category,
      });
    } else {
      console.log('🚀 Review editado guardado en MongoDB!');
      showSuccessMessage('Review edited successfully!');
    }
  };

  // Cancel editing
  const cancelEdit = () => {
    setEditingItem(null);
    setEditError('');
  };

  // Load initial data
  useEffect(() => {
    fetchDataLists();
    fetchTenants();
  }, []);

  // Load software inventory when tenant, page, or filter changes
  useEffect(() => {
    if (selectedTenant && selectedTenant.id !== 'all-tenants') {
      fetchSoftwareInventory(selectedTenant.id, currentPage, filter);
    }
  }, [selectedTenant, currentPage, filter]);

  // Manejar eventos SSE
  useEffect(() => {
    if (lastEvent && selectedTenant?.id !== 'all-tenants') {
      const itemId = lastEvent.itemId.replace(`${lastEvent.tenant_id}-`, '');
      
      // Buscar el item por nombre de software
      const item = softwareItems.find(item => 
        item.name === lastEvent.software_name
      );
      
      if (item) {
        // Actualizar el item en el estado
        updateItemInState(item.id, { 
          feedback: lastEvent.feedback 
        });
        
        // Mostrar notificación de cambio
        showSuccessMessage(`${lastEvent.software_name} was updated by another user`);
      }
    }
  }, [lastEvent]);

  // Mostrar status de SSE por unos segundos cuando se conecta/desconecta
  useEffect(() => {
    if (selectedTenant?.id !== 'all-tenants') {
      setShowSSEStatus(true);
      const timer = setTimeout(() => setShowSSEStatus(false), 3000);
      return () => clearTimeout(timer);
    }
  }, [sseConnected, selectedTenant]);

  // Note: We now use the complete lists from JSON files for filters
  // instead of just the values present in the current tenant data

  // Note: Commercial name and category filtering is now handled server-side
  // softwareItems already contains the filtered results for the current page
  const clientFilteredItems = softwareItems;

  // Calculate local statistics for real-time updates
  const localStats = calculateLocalStats();

  // For filter buttons, we need to use the server-provided total counts (apiFilteredCounts)
  // and only apply optimistic updates for items that changed locally
  // Note: This is a simplified approach - for full accuracy we'd need to track
  // which items changed and adjust the total counts accordingly
  const displayFilterCounts = apiFilteredCounts;

  // Reset page and refresh data when additional filters change
  useEffect(() => {
    setCurrentPage(1);
    if (selectedTenant && selectedTenant.id !== 'all-tenants') {
      fetchSoftwareInventory(selectedTenant.id, 1, filter);
    }
  }, [commercialNameFilter, categoryFilter]);

  // Show loading screen while checking authentication
  if (authLoading) {
    return <AuthLoading />;
  }

  return (
    <div className='min-h-screen bg-gray-50'>
      {/* Fixed Success Message */}
      {successMessage && (
        <div className='fixed top-4 left-1/2 transform -translate-x-1/2 z-50 px-6 py-3 bg-green-500 bg-opacity-70 border border-green-300 rounded-lg shadow-lg'>
          <p className='text-white text-sm font-medium'>✅ {successMessage}</p>
        </div>
      )}

      {/* SSE Status Indicator */}
      {showSSEStatus && selectedTenant?.id !== 'all-tenants' && (
        <div className={`fixed top-16 left-1/2 transform -translate-x-1/2 z-50 px-4 py-2 rounded-lg shadow-lg transition-all ${
          sseConnected 
            ? 'bg-blue-500 bg-opacity-70 border border-blue-300' 
            : 'bg-orange-500 bg-opacity-70 border border-orange-300'
        }`}>
          <p className='text-white text-xs font-medium'>
            {sseConnected ? '🔄 Real-time updates active' : '⚠️ Reconnecting...'}
          </p>
        </div>
      )}

      {/* Header */}
      <header className='bg-white border-b border-gray-200 px-6 py-4'>
        <div className='flex items-center justify-between'>
          <div className='flex items-center space-x-2'>
            <span className='img-logo'>
              <Image
                src='/batuta-logo-dark.svg'
                alt='logo'
                width={100}
                height={100}
              />
            </span>
          </div>
          <div className='flex items-center space-x-4'>
            <Button
              variant='outline'
              size='sm'
              onClick={logout}
              className='flex items-center space-x-2'
            >
              <LogOut className='h-4 w-4' />
              <span>Logout</span>
            </Button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className='px-6 py-8'>
        <div className='max-w-7xl mx-auto'>
          {/* Title and Tenant Selector */}
          <div className='flex items-center justify-between mb-8'>
            <h1 className='text-2xl font-semibold text-gray-900'>
              Software Classifier
            </h1>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant='outline'
                  className='bg-gray-800 text-white hover:bg-gray-700 border-gray-800'
                  disabled={tenantsLoading || loading}
                >
                  {tenantsLoading
                    ? 'Loading tenants...'
                    : loading
                      ? `Loading ${selectedTenant?.name || 'tenant'}...`
                      : selectedTenant
                        ? selectedTenant.name
                        : 'Select Tenant'}
                  <ChevronDown className='ml-2 h-4 w-4' />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent
                align='end'
                className='w-[200px] max-h-[300px] overflow-y-auto'
              >
                {/* All Tenants option */}
                <DropdownMenuItem
                  onClick={() => handleTenantSelect(ALL_TENANTS_OPTION)}
                  className={
                    selectedTenant?.id === 'all-tenants' ? 'bg-gray-100' : ''
                  }
                >
                  {ALL_TENANTS_OPTION.name}
                </DropdownMenuItem>
                {tenants.length > 0 && (
                  <div className='border-t my-1' />
                )}
                {tenants.map(tenant => (
                  <DropdownMenuItem
                    key={tenant.id}
                    onClick={() => handleTenantSelect(tenant)}
                    className={
                      selectedTenant?.id === tenant.id ? 'bg-gray-100' : ''
                    }
                  >
                    {tenant.name}
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          {/* Classification Reviews Statistics */}
          {reviewStats && (
            <div className='bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6'>
              <h3 className='text-lg font-medium text-gray-900 mb-4'>
                Classification Reviews Statistics {selectedTenant?.id === 'all-tenants' ? 'for all tenants' : selectedTenant ? `for ${selectedTenant.name}` : ''}
              </h3>
              <div className='grid grid-cols-2 md:grid-cols-6 gap-6'>
                <div className='text-center'>
                  <div className='text-xl font-bold text-green-600 mb-1'>{reviewStats.totalCorrect}</div>
                  <div className='text-sm text-gray-500 font-medium'>Correct</div>
                </div>
                <div className='text-center'>
                  <div className='text-xl font-bold text-red-600 mb-1'>{reviewStats.totalEdited}</div>
                  <div className='text-sm text-gray-500 font-medium'>Edited</div>
                </div>
                <div className='text-center'>
                  <div className='text-xl font-bold text-gray-800 mb-1'>{reviewStats.totalReviews}</div>
                  <div className='text-sm text-gray-500 font-medium'>Total</div>
                </div>
                <div className='text-center'>
                  <div className='text-xl font-bold text-blue-600 mb-1'>{reviewStats.accuracyRate}%</div>
                  <div className='text-sm text-gray-500 font-medium'>Accuracy</div>
                </div>
                <div className='text-center'>
                  <div className='text-xl font-bold text-amber-400 mb-1'>{reviewStats.categoriesChanged}</div>
                  <div className='text-sm text-gray-500 font-medium'>Cat. Changed</div>
                </div>
                <div className='text-center'>
                  <div className='text-xl font-bold text-emerald-400 mb-1'>{reviewStats.commercialNamesChanged}</div>
                  <div className='text-sm text-gray-500 font-medium'>Names Changed</div>
                </div>
              </div>
            </div>
          )}

          {/* Additional Filters - only show when a specific tenant is selected */}
          {selectedTenant && selectedTenant.id !== 'all-tenants' && (
          <div className='flex space-x-4 mb-6'>
            <div className='flex flex-col'>
              <label className='text-sm font-medium text-gray-700 mb-2'>
                Commercial Name
              </label>
              <NavigableDropdown
                value={commercialNameFilter}
                onChange={setCommercialNameFilter}
                options={dataLists.commercialNames}
                placeholder='commercial names'
                showAllOption={true}
                allLabel='All Commercial Names'
                disabled={dataListsLoading}
              />
            </div>

            <div className='flex flex-col'>
              <label className='text-sm font-medium text-gray-700 mb-2'>
                Category
              </label>
              <NavigableDropdown
                value={categoryFilter}
                onChange={setCategoryFilter}
                options={dataLists.categories}
                placeholder='categories'
                showAllOption={true}
                allLabel='All Categories'
                disabled={dataListsLoading}
              />
            </div>

            {/* Clear Filters Button */}
            {(commercialNameFilter !== 'all' || categoryFilter !== 'all') && (
              <div className='flex flex-col justify-end'>
                <Button
                  variant='ghost'
                  onClick={() => {
                    setCommercialNameFilter('all');
                    setCategoryFilter('all');
                  }}
                  className='text-gray-500 hover:text-gray-700'
                  disabled={dataListsLoading}
                >
                  Clear Filters
                </Button>
              </div>
            )}
          </div>
          )}

          {/* Status Filter Buttons - only show when a specific tenant is selected */}
          {selectedTenant && selectedTenant.id !== 'all-tenants' && (
          <div className='flex space-x-2 mb-6'>
            <Button
              variant={filter === 'positive' ? 'default' : 'secondary'}
              onClick={() => handleFilterChange('positive')}
              className={`${filter === 'positive'
                ? 'bg-gray-800 text-white hover:bg-gray-700'
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                }`}
            >
              Positive {displayFilterCounts.positive}
            </Button>
            <Button
              variant={filter === 'negative' ? 'default' : 'secondary'}
              onClick={() => handleFilterChange('negative')}
              className={`${filter === 'negative'
                ? 'bg-gray-800 text-white hover:bg-gray-700'
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                }`}
            >
              Edited {displayFilterCounts.edited}
            </Button>
            <Button
              variant={filter === 'not-reviewed' ? 'default' : 'secondary'}
              onClick={() => handleFilterChange('not-reviewed')}
              className={`${filter === 'not-reviewed'
                ? 'bg-gray-800 text-white hover:bg-gray-700'
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                }`}
            >
              Not Reviewed {displayFilterCounts.notReviewed}
            </Button>
          </div>
          )}

          {/* Error Display */}
          {reviewsError && (
            <div className='bg-red-50 border border-red-200 rounded-lg p-4 mb-6'>
              <p className='text-red-600 text-sm'>❌ MongoDB Error: {reviewsError}</p>
            </div>
          )}

          {/* Edit Error Message */}
          {editError && (
            <div className='mb-4 p-3 bg-red-50 border border-red-200 rounded-md'>
              <p className='text-red-600 text-sm'>{editError}</p>
            </div>
          )}

          {/* Software Table */}
          <div className='bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden'>
            <div className='overflow-x-auto'>
              <table className='w-full'>
                <thead className='bg-gray-50 border-b border-gray-200'>
                  <tr>
                    <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                      Name
                    </th>
                    <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                      Vendor
                    </th>
                    <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                      Commercial Name
                    </th>
                    <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                      Category
                    </th>
                    <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                      Feedback
                    </th>
                  </tr>
                </thead>
                <tbody className='bg-white divide-y divide-gray-200'>
                  {loading ? (
                    <tr>
                      <td
                        colSpan={5}
                        className='px-6 py-8 text-center text-gray-500'
                      >
                        Loading software inventory...
                      </td>
                    </tr>
                  ) : !selectedTenant || selectedTenant.id === 'all-tenants' ? (
                    <tr>
                      <td
                        colSpan={5}
                        className='px-6 py-12 text-center'
                      >
                        <div className='flex flex-col items-center space-y-3'>
                          <div className='text-gray-400 text-lg'>👆</div>
                          <div className='text-gray-600 font-medium'>
                            {selectedTenant?.id === 'all-tenants' 
                              ? 'Viewing global statistics for all tenants'
                              : 'Select a tenant to start reviewing Software Classifier'
                            }
                          </div>
                          <div className='text-gray-500 text-sm'>
                            {selectedTenant?.id === 'all-tenants'
                              ? 'Choose a specific tenant from the dropdown above to review their software inventory'
                              : 'Choose a tenant from the dropdown above to view their software inventory'
                            }
                          </div>
                        </div>
                      </td>
                    </tr>
                  ) : clientFilteredItems.length === 0 ? (
                    <tr>
                      <td
                        colSpan={5}
                        className='px-6 py-8 text-center text-gray-500'
                      >
                        No software items found for this filter
                      </td>
                    </tr>
                  ) : (
                    clientFilteredItems.map(item => (
                      <tr key={item.id} className='hover:bg-gray-50'>
                        <td
                          className='px-6 py-4 text-sm text-gray-900 max-w-xs truncate'
                          title={item.name}
                        >
                          {item.name}
                        </td>
                        <td className='px-6 py-4 text-sm text-gray-500'>
                          {item.provider}
                        </td>
                        <td className='px-6 py-4 text-sm text-gray-500'>
                          {editingItem?.id === item.id ? (
                            <NavigableDropdown
                              value={editingItem.commercialName}
                              onChange={(value: string) =>
                                handleEditChange('commercialName', value)
                              }
                              options={dataLists.commercialNames}
                              placeholder='Select commercial name'
                              allowNew={true}
                              onAddNew={addNewCommercialName}
                              className='w-full'
                            />
                          ) : (
                            item.commercialName || '-'
                          )}
                        </td>
                        <td className='px-6 py-4 text-sm text-gray-500'>
                          {editingItem?.id === item.id ? (
                            <NavigableDropdown
                              value={editingItem.category}
                              onChange={(value: string) =>
                                handleEditChange('category', value)
                              }
                              options={dataLists.categories}
                              placeholder='Select category'
                              strict={true}
                              className='w-full'
                            />
                          ) : (
                            item.category || '-'
                          )}
                        </td>
                        <td className='px-6 py-4'>
                          {editingItem?.id === item.id ? (
                            <div className='flex space-x-2'>
                              <Button
                                size='sm'
                                onClick={confirmEdit}
                                disabled={pendingOperations.has(item.id)}
                                className={`p-2 rounded-full bg-green-100 text-green-600 hover:bg-green-200 ${pendingOperations.has(item.id) ? 'opacity-50' : ''}`}
                              >
                                {pendingOperations.has(item.id) ? (
                                  <Loader2 className='h-4 w-4 animate-spin' />
                                ) : (
                                  <Check className='h-4 w-4' />
                                )}
                              </Button>
                              <Button
                                size='sm'
                                variant='ghost'
                                onClick={cancelEdit}
                                className='p-2 rounded-full text-gray-400 hover:text-red-600 hover:bg-red-50'
                              >
                                <X className='h-4 w-4' />
                              </Button>
                            </div>
                          ) : (
                            <div className='flex space-x-2'>
                              {/* Only show thumbs up if not corrected */}
                              {item.feedback !== 'corrected' && (
                                <Button
                                  size='sm'
                                  variant='ghost'
                                  onClick={() =>
                                    handleFeedback(item.id, 'positive')
                                  }
                                  disabled={pendingOperations.has(item.id)}
                                  className={`p-2 rounded-full ${item.feedback === 'positive'
                                    ? 'bg-green-100 text-green-600 hover:bg-green-200'
                                    : 'text-gray-400 hover:text-green-600 hover:bg-green-50'
                                    } ${pendingOperations.has(item.id) ? 'opacity-50' : ''}`}
                                >
                                  {pendingOperations.has(item.id) ? (
                                    <Loader2 className='h-4 w-4 animate-spin' />
                                  ) : (
                                    <ThumbsUp className='h-4 w-4' />
                                  )}
                                </Button>
                              )}
                              <Button
                                size='sm'
                                variant='ghost'
                                onClick={() =>
                                  handleFeedback(item.id, 'negative')
                                }
                                disabled={pendingOperations.has(item.id)}
                                className={`p-2 rounded-full ${item.feedback === 'negative'
                                  ? 'bg-red-100 text-red-600 hover:bg-red-200'
                                  : 'text-gray-400 hover:text-red-600 hover:bg-red-50'
                                  } ${pendingOperations.has(item.id) ? 'opacity-50' : ''}`}
                              >
                                {pendingOperations.has(item.id) ? (
                                  <Loader2 className='h-4 w-4 animate-spin' />
                                ) : (
                                  <ThumbsDown className='h-4 w-4' />
                                )}
                              </Button>
                              {item.feedback === 'corrected' && (
                                <Button
                                  size='sm'
                                  variant='ghost'
                                  className='p-2 rounded-full bg-blue-100 text-blue-600 cursor-default'
                                  disabled
                                >
                                  <CheckCircle className='h-4 w-4' />
                                </Button>
                              )}
                            </div>
                          )}
                        </td>
                      </tr>
                    ))
                  )}
                </tbody>
              </table>
            </div>
          </div>

          {/* Pagination */}
          {pagination && pagination.totalPages > 1 && (
            <div className='flex items-center justify-between mt-6'>
              <div className='text-sm text-gray-700'>
                Showing{' '}
                {(pagination.currentPage - 1) * pagination.itemsPerPage + 1} to{' '}
                {Math.min(
                  pagination.currentPage * pagination.itemsPerPage,
                  pagination.totalItems
                )}{' '}
                of {pagination.totalItems} results
                {filter !== 'all' && (
                  <span className='ml-1 text-gray-500'>
                    (filtered from {totalCounts.all} total)
                  </span>
                )}
              </div>

              <div className='flex items-center space-x-2'>
                <Button
                  variant='outline'
                  size='sm'
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={!pagination.hasPreviousPage}
                  className='flex items-center'
                >
                  <ChevronLeft className='h-4 w-4 mr-1' />
                  Previous
                </Button>

                <div className='flex items-center space-x-1'>
                  {Array.from(
                    { length: Math.min(5, pagination.totalPages) },
                    (_, i) => {
                      let pageNum;
                      if (pagination.totalPages <= 5) {
                        pageNum = i + 1;
                      } else if (pagination.currentPage <= 3) {
                        pageNum = i + 1;
                      } else if (
                        pagination.currentPage >=
                        pagination.totalPages - 2
                      ) {
                        pageNum = pagination.totalPages - 4 + i;
                      } else {
                        pageNum = pagination.currentPage - 2 + i;
                      }

                      return (
                        <Button
                          key={pageNum}
                          variant={
                            pageNum === pagination.currentPage
                              ? 'default'
                              : 'outline'
                          }
                          size='sm'
                          onClick={() => handlePageChange(pageNum)}
                          className={`w-8 h-8 p-0 ${pageNum === pagination.currentPage
                            ? 'bg-gray-800 text-white'
                            : 'text-gray-700'
                            }`}
                        >
                          {pageNum}
                        </Button>
                      );
                    }
                  )}
                </div>

                <Button
                  variant='outline'
                  size='sm'
                  onClick={() => handlePageChange(currentPage + 1)}
                  disabled={!pagination.hasNextPage}
                  className='flex items-center'
                >
                  Next
                  <ChevronRight className='h-4 w-4 ml-1' />
                </Button>
              </div>
            </div>
          )}
        </div>
      </main>

      {/* Footer */}
      <footer className='bg-white border-t border-gray-200 py-6 mt-8'>
        <div className='max-w-7xl mx-auto px-6'>
          <div className='text-center text-gray-500 text-sm'>
            Powered by the Batuta AI Team ⚡
          </div>
        </div>
      </footer>
    </div>
  );
}
