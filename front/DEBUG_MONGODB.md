# 🔧 Diagnóstico y Solución de Error MongoDB

## 🚨 Problema Actual
```
❌ MongoDB Error: Error cargando datos
```

## 📋 Pasos para Solucionar

### 1. **PRIMERO: Actualizar MONGO_URI**
Tu `MONGO_URI` está incompleta. Necesitas agregar usuario y contraseña:

```bash
# Archivo: front/.env.local
# Reemplaza USER_NAME y PASSWORD con tus credenciales reales de MongoDB Atlas
MONGO_URI=mongodb+srv://USER_NAME:<EMAIL>/software-classifier?retryWrites=true&w=majority&ssl=true
```

**Para obtener las credenciales:**
1. Ve a MongoDB Atlas (https://cloud.mongodb.com)
2. Ve a Database Access
3. Crea un usuario o usa uno existente
4. Reemplaza USER_NAME y PASSWORD en la URI

### 2. **Probar conexión con herramienta de debug**
```bash
# Ejecuta en tu terminal (dentro de la carpeta front)
curl http://localhost:3000/api/debug
```

Esto te dará información detallada sobre qué está fallando.

### 3. **Verificar IP Whitelist en MongoDB Atlas**
1. Ve a MongoDB Atlas → Network Access
2. Asegúrate de que tu IP esté en la whitelist
3. O agrega `0.0.0.0/0` para permitir todas las IPs (solo para testing)

### 4. **Verificar en consola del navegador**
1. Abre F12 en tu navegador
2. Ve a la pestaña Console
3. Busca logs como:
   - `🔄 Cargando reviews desde MongoDB...`
   - `✅ Conectado a MongoDB - Base de datos: software-classifier`
   - O errores específicos

### 5. **Probar APIs individuales**
```bash
# Probar cada API por separado
curl http://localhost:3000/api/reviews/stats
curl http://localhost:3000/api/reviews/correct
curl http://localhost:3000/api/reviews/edited
```

## 🔍 Posibles Causas del Error

### A. **MONGO_URI incorrecta** (MÁS PROBABLE)
- Falta usuario/contraseña
- Formato incorrecto
- Credenciales incorrectas

### B. **Certificado SSL**
- El certificado está correcto en tu .env.local
- Problema podría ser permisos de archivo

### C. **IP no whitelisteada**
- MongoDB Atlas bloquea IPs no autorizadas

### D. **Timeout de conexión**
- La conexión está tardando demasiado

## 🛠 Solución Rápida

### Opción 1: URI Completa
Actualiza tu `.env.local` con la URI completa:
```bash
MONGO_URI=mongodb+srv://TU_USUARIO:<EMAIL>/software-classifier?retryWrites=true&w=majority&ssl=true
```

### Opción 2: Sin SSL (para prueba temporal)
Si quieres probar sin certificados SSL temporalmente:
```bash
MONGO_URI=mongodb+srv://TU_USUARIO:<EMAIL>/software-classifier?retryWrites=true&w=majority
```

Y comenta el certificado:
```bash
# DB_CERT="-----BEGIN CERTIFICATE-----
# ... (comentar todo el certificado)
# -----END PRIVATE KEY-----"
```

## 📞 Debug Detallado

### 1. Ejecutar diagnóstico:
```bash
# En tu navegador:
http://localhost:3000/api/debug
```

### 2. Ver logs en tiempo real:
1. Abre la consola del navegador (F12)
2. Refresca la página
3. Busca mensajes de MongoDB

### 3. Verificar variables de entorno:
```bash
# En tu terminal, dentro de front/:
node -e "console.log('MONGO_URI:', process.env.MONGO_URI?.substring(0, 50))"
```

## ✅ Verificación Final

Una vez solucionado, deberías ver:
1. ✅ `Conectado a MongoDB - Base de datos: software-classifier` en consola
2. ✅ Sección azul de estadísticas sin errores
3. ✅ Los botones thumbs up/down funcionando
4. ✅ Datos apareciendo en MongoDB Compass

## 🆘 Si Nada Funciona

Comparte el resultado de:
```bash
curl http://localhost:3000/api/debug
```

Y los logs de la consola del navegador para diagnosticar específicamente qué está fallando.
