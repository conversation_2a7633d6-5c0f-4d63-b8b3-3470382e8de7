import { useState, useEffect, useRef } from 'react';

interface SSEEvent {
  event: string;
  type: 'correct' | 'edited';
  itemId: string;
  tenant_id: string;
  software_name: string;
  feedback: 'positive' | 'corrected' | null;
  timestamp: number;
}

export function useSSE(tenantId?: string) {
  const [connected, setConnected] = useState(false);
  const [lastEvent, setLastEvent] = useState<SSEEvent | null>(null);
  const eventSourceRef = useRef<EventSource | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const connect = () => {
    if (eventSourceRef.current) {
      eventSourceRef.current.close();
    }

    const url = tenantId 
      ? `/api/sse/reviews?tenant_id=${tenantId}`
      : '/api/sse/reviews';
    
    const eventSource = new EventSource(url);
    eventSourceRef.current = eventSource;

    eventSource.onopen = () => {
      setConnected(true);
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
        reconnectTimeoutRef.current = null;
      }
    };

    eventSource.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        if (data.type !== 'connected') {
          setLastEvent(data);
        }
      } catch (error) {
        console.error('Error parsing SSE event:', error);
      }
    };

    eventSource.onerror = () => {
      setConnected(false);
      eventSource.close();
      
      // Reconectar después de 3 segundos
      reconnectTimeoutRef.current = setTimeout(() => {
        connect();
      }, 3000);
    };
  };

  const disconnect = () => {
    if (eventSourceRef.current) {
      eventSourceRef.current.close();
      eventSourceRef.current = null;
    }
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }
    setConnected(false);
  };

  useEffect(() => {
    if (tenantId && tenantId !== 'all-tenants') {
      connect();
    } else {
      disconnect();
    }

    return () => {
      disconnect();
    };
  }, [tenantId]);

  useEffect(() => {
    return () => {
      disconnect();
    };
  }, []);

  return {
    connected,
    lastEvent,
    reconnect: connect
  };
}