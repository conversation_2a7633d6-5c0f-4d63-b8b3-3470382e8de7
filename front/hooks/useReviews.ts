import { useState, useEffect, useCallback } from 'react';

export interface ReviewCorrectItem {
  _id?: string;
  software_name: string;
  category: string;
  commercial_name?: string;
  confidence_score?: number;
  reviewer?: string;
  tenant_id?: string;
  notes?: string;
  created_at: Date;
}

export interface ReviewEditedItem {
  _id?: string;
  software_name: string;
  original_category: string;
  original_commercial_name?: string;
  corrected_category: string;
  corrected_commercial_name?: string;
  confidence_score?: number;
  reviewer?: string;
  tenant_id?: string;
  notes?: string;
  created_at: Date;
}

export interface ReviewStats {
  totalCorrect: number;
  totalEdited: number;
  totalReviews: number;
  accuracyRate: number;
  categoriesChanged: number;
  commercialNamesChanged: number;
}

export function useReviews(tenantId?: string) {
  const [reviewsCorrect, setReviewsCorrect] = useState<ReviewCorrectItem[]>([]);
  const [reviewsEdited, setReviewsEdited] = useState<ReviewEditedItem[]>([]);
  const [stats, setStats] = useState<ReviewStats | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Cargar todos los reviews
  const loadReviews = useCallback(async () => {
    setLoading(true);
    setError(null);
    
    try {
      const params = tenantId ? `?tenant_id=${tenantId}` : '';
      
      console.log('🔄 Cargando reviews desde MongoDB...');
      
      const [correctResponse, editedResponse, statsResponse] = await Promise.all([
        fetch(`/api/reviews/correct${params}`),
        fetch(`/api/reviews/edited${params}`),
        fetch(`/api/reviews/stats${params}`)
      ]);

      // Log responses for debugging
      console.log('API Responses:', {
        correct: correctResponse.status,
        edited: editedResponse.status,
        stats: statsResponse.status
      });

      if (!correctResponse.ok || !editedResponse.ok || !statsResponse.ok) {
        const errorDetails = [];
        if (!correctResponse.ok) errorDetails.push(`correct: ${correctResponse.status}`);
        if (!editedResponse.ok) errorDetails.push(`edited: ${editedResponse.status}`);
        if (!statsResponse.ok) errorDetails.push(`stats: ${statsResponse.status}`);
        
        throw new Error(`API Error - ${errorDetails.join(', ')}`);
      }

      const [correctData, editedData, statsData] = await Promise.all([
        correctResponse.json(),
        editedResponse.json(),
        statsResponse.json()
      ]);

      console.log('✅ Reviews cargados exitosamente:', {
        correct: correctData.count || 0,
        edited: editedData.count || 0,
        stats: statsData.stats
      });

      setReviewsCorrect(correctData.reviews || []);
      setReviewsEdited(editedData.reviews || []);
      setStats(statsData.stats || null);
    } catch (err: any) {
      console.error('❌ Error cargando reviews:', err);
      setError(`Error cargando datos: ${err.message}`);
    } finally {
      setLoading(false);
    }
  }, [tenantId]);

  // Agregar review correcto (👍)
  const addReviewCorrect = useCallback(async (reviewData: {
    software_name: string;
    category: string;
    commercial_name?: string;
    confidence_score?: number;
    notes?: string;
  }) => {
    try {
      const response = await fetch('/api/reviews/correct', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...reviewData,
          tenant_id: tenantId,
          reviewer: 'user'
        })
      });

      if (!response.ok) {
        throw new Error('Error agregando review correcto');
      }

      // Recargar datos para obtener la info actualizada
      await loadReviews();
      return true;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Error desconocido');
      return false;
    }
  }, [tenantId, loadReviews]);

  // Agregar review editado (👎)
  const addReviewEdited = useCallback(async (reviewData: {
    software_name: string;
    original_category: string;
    original_commercial_name?: string;
    corrected_category: string;
    corrected_commercial_name?: string;
    confidence_score?: number;
    notes?: string;
  }) => {
    try {
      const response = await fetch('/api/reviews/edited', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...reviewData,
          tenant_id: tenantId,
          reviewer: 'user'
        })
      });

      if (!response.ok) {
        throw new Error('Error agregando review editado');
      }

      // Recargar datos para obtener la info actualizada
      await loadReviews();
      return true;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Error desconocido');
      return false;
    }
  }, [tenantId, loadReviews]);

  // Buscar por software, categoría o nombre comercial
  const searchReviews = useCallback(async (searchParams: {
    software_name?: string;
    category?: string;
    commercial_name?: string;
  }) => {
    setLoading(true);
    setError(null);
    
    try {
      const params = new URLSearchParams({
        ...(searchParams.software_name && { software_name: searchParams.software_name }),
        ...(searchParams.category && { category: searchParams.category }),
        ...(searchParams.commercial_name && { commercial_name: searchParams.commercial_name }),
        ...(tenantId && { tenant_id: tenantId })
      });
      
      const response = await fetch(`/api/reviews/search?${params}`);
      
      if (!response.ok) {
        throw new Error('Error en la búsqueda');
      }

      const data = await response.json();
      return data.results;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Error desconocido');
      return null;
    } finally {
      setLoading(false);
    }
  }, [tenantId]);

  // Cargar datos al montar el componente
  useEffect(() => {
    loadReviews();
  }, [loadReviews]);

  return {
    reviewsCorrect,
    reviewsEdited,
    stats,
    loading,
    error,
    addReviewCorrect,
    addReviewEdited,
    searchReviews,
    refreshData: loadReviews
  };
}

// Hook para estadísticas en tiempo real
export function useReviewStats(tenantId?: string, refreshInterval = 30000) {
  const [stats, setStats] = useState<ReviewStats | null>(null);
  const [loading, setLoading] = useState(false);

  const loadStats = useCallback(async () => {
    setLoading(true);
    try {
      const params = tenantId ? `?tenant_id=${tenantId}` : '';
      const response = await fetch(`/api/reviews/stats${params}`);
      
      if (response.ok) {
        const data = await response.json();
        setStats(data.stats);
      }
    } catch (error) {
      console.error('Error cargando estadísticas:', error);
    } finally {
      setLoading(false);
    }
  }, [tenantId]);

  useEffect(() => {
    loadStats();
    
    // Actualizar estadísticas cada cierto tiempo
    const interval = setInterval(loadStats, refreshInterval);
    
    return () => clearInterval(interval);
  }, [loadStats, refreshInterval]);

  return { stats, loading, refreshStats: loadStats };
}

// Hook para tendencias de corrección
export function useCorrectionTrends(tenantId?: string) {
  const [trends, setTrends] = useState<{
    categoryCorrections: Array<{ from: string; to: string; count: number }>;
    commercialNameCorrections: Array<{ from: string; to: string; count: number }>;
  } | null>(null);
  const [loading, setLoading] = useState(false);

  const loadTrends = useCallback(async () => {
    setLoading(true);
    try {
      const params = tenantId ? `?tenant_id=${tenantId}` : '';
      const response = await fetch(`/api/reviews/trends${params}`);
      
      if (response.ok) {
        const data = await response.json();
        setTrends(data.trends);
      }
    } catch (error) {
      console.error('Error cargando tendencias:', error);
    } finally {
      setLoading(false);
    }
  }, [tenantId]);

  useEffect(() => {
    loadTrends();
  }, [loadTrends]);

  return { trends, loading, refreshTrends: loadTrends };
}
